<?php

return [
    'disable' => env('CAPTCHA_DISABLE', false),

    'characters' => ['2', '3', '4', '6', '7', '8', '9'],

    'default' => [
        'length' => 5,
        'width' => 160,
        'height' => 30,
        'quality' => 300,
        'math' => false,
        'expire' => 60,
        'encrypt' => false,
        'lines' => -2,
        'bgImage' => true,
        'bgColor' => '#ffffff',
        'fontColors' => ['#e74c3c', '#2ecc71', '#3498db', '#9b59b6', '#f1c40f', '#1abc9c', '#e67e22'],
        'angle' => 5,
        'blur' => 1,
        'sharpen' => 3,
    ],

    'math' => [
        'length' => 9,
        'width' => 120,
        'height' => 36,
        'quality' => 90,
        'math' => true,
    ],

    'flat' => [
        'length' => 6,
        'width' => 160,
        'height' => 46,
        'quality' => 90,
        'lines' => 6,
        'bgImage' => false,
        'bgColor' => '#ecf2f4',
        'fontColors' => ['#2c3e50', '#c0392b', '#c0392b', '#8e44ad', '#303f9f', '#f57c00', '#795548'],
        'contrast' => -5,
    ],

    'mini' => [
        'length' => 3,
        'width' => 60,
        'height' => 32,
    ],

    'inverse' => [
        'length' => 5,
        'width' => 120,
        'height' => 36,
        'quality' => 90,
        'sensitive' => true,
        'angle' => 12,
        'sharpen' => 10,
        'blur' => 2,
        'invert' => true,
        'contrast' => -5,
    ],
];
