<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAffectationAgentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
 
    public function rules(): array
    {
        return [
            'id_agent' => [
                'required',
                'exists:admins,id',
                function ($attribute, $value, $fail) {
                    $overlappingAffectation = \App\Models\AffectationAgent::where('id_agent', $value)
                        ->where(function ($query) {
                            $query->whereBetween('date_start', [$this->date_start, $this->date_end])
                                ->orWhereBetween('date_end', [$this->date_start, $this->date_end])
                                ->orWhere(function ($q) {
                                    $q->where('date_start', '<=', $this->date_start)
                                        ->where('date_end', '>=', $this->date_end);
                                });
                        })
                        ->exists();

                    if ($overlappingAffectation) {
                        $fail('This agent already has an affectation during this period.');
                    }
                }
            ],
            'id_sale_point' => 'required|exists:sale_points,id',
            'id_sale_period' => 'required|exists:sale_periods,id',
            'payment_methods' => 'required|array',
            'payment_methods.*' => 'required|exists:payment_methods,id',
            'date_start' => 'required|date',
            'date_end' => 'required|date|after:date_start',
        ];
    }
}

