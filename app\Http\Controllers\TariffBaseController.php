<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTariffBaseRequest;
use App\Http\Requests\UpdateTariffBaseRequest;
use App\Http\Resources\TariffBaseResource;
use App\Models\TariffBase;
use App\Repositories\TariffBaseRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;

class TariffBaseController extends Controller
{
    private TariffBaseRepository $repository;

    public function __construct(TariffBaseRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(TariffBase::class, 'tariff_base');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return TariffBaseResource::collection(
            $this->repository
                ->with(['subsType', 'tariffs'])
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return TariffBaseResource::collection($this->repository->with(['subsType', 'tariffs'])->all());
    }

    public function getBySubsType($subsTypeId): AnonymousResourceCollection
    {
        $tariffBases = $this->repository->with(['subsType', 'tariffs'])
            ->findWhere(['id_subs_type' => $subsTypeId]);
        return TariffBaseResource::collection($tariffBases);
    }

    public function store(StoreTariffBaseRequest $request): JsonResponse
    {
        $data = $request->validated();
        $tariffsData = $data['tariffs'];
        unset($data['tariffs']);

        $tariffBase = $this->repository->create($data);

        // Create associated tariffs
        foreach ($tariffsData as $tariffData) {
            $tariffData['id_tariff_base'] = $tariffBase->id;
            $tariffBase->tariffs()->create($tariffData);
        }

        $tariffBase->load(['subsType', 'tariffs']);

        return response()->json([
            'message' => 'Tariff base created successfully',
            'data' => new TariffBaseResource($tariffBase)
        ], 201);
    }

    public function show(TariffBase $tariffBase): TariffBaseResource
    {
        $tariffBase->load(['subsType', 'tariffs']);
        return new TariffBaseResource($tariffBase);
    }

    public function update(UpdateTariffBaseRequest $request, TariffBase $tariffBase): JsonResponse
    {
        $data = $request->validated();
        $tariffsData = $data['tariffs'];
        unset($data['tariffs']);

        $tariffBase = $this->repository->update($data, $tariffBase->id);

        // Delete existing tariffs and create new ones
        $tariffBase->tariffs()->delete();
        foreach ($tariffsData as $tariffData) {
            $tariffData['id_tariff_base'] = $tariffBase->id;
            $tariffBase->tariffs()->create($tariffData);
        }

        $tariffBase->load(['subsType', 'tariffs']);

        return response()->json([
            'message' => 'Tariff base updated successfully',
            'data' => new TariffBaseResource($tariffBase)
        ]);
    }

    public function destroy(TariffBase $tariffBase): JsonResponse
    {
        $this->repository->delete($tariffBase->id);

        return response()->json([
            'message' => 'Tariff base deleted successfully'
        ]);
    }
}

