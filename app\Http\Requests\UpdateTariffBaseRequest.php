<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTariffBaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'id_subs_type' => 'required|exists:subs_types,id',
            'is_regular' => 'required|boolean',
            'is_triff_fixed' => 'nullable|boolean',
            'km_start' => 'nullable|integer|min:0',
            'km_end' => 'nullable|integer|min:0|gte:km_start',
            'tariffs' => 'required|array|min:1',
            'tariffs.*.tariff' => 'required|numeric|min:0',
            'tariffs.*.date_subscription' => 'required|date',
            'tariffs.*.date_website' => 'required|date'
        ];
    }
}



