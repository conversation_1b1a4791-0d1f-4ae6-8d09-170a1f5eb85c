<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\AffectationAgent;

class AffectationAgentPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, AffectationAgent $affectationAgent): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, AffectationAgent $affectationAgent): bool
    {
        return true;
    }

    public function delete(Admin $admin, AffectationAgent $affectationAgent): bool
    {
        return true;
    }
}