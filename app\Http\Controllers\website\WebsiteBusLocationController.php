<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Http\Requests\GetBusLocationAmountRequest;
use App\Models\Config;
use App\Models\LocationType;
use App\Models\TypeVehiculeSaisonLocation;
use Illuminate\Http\JsonResponse;


class WebsiteBusLocationController extends Controller
{
    public function getLocationAmount(GetBusLocationAmountRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            // Get the location type with its vehicle type relationships
            $locationType = LocationType::with(['typeVehicleTypeLocations.typeVehicule'])
                ->findOrFail($validated['id_location_type']);

            // Find matching vehicle type configuration
            $vehicleTypeConfig = $locationType->typeVehicleTypeLocations
                ->first(function($config) use ($validated) {
                    return $config->id_type_vehicule === $validated['id_vehicule_type'] &&
                           $config->status === true;
                });

            if (!$vehicleTypeConfig) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No valid configuration found for the specified parameters'
                ], 404);
            }

            // Find the season price based on the date range
            $seasonPrice = TypeVehiculeSaisonLocation::query()
                ->where('id_type_vehicule', $validated['id_vehicule_type'])
                ->whereHas('season', function($query) {
                    $query->where('status', true);
                })
                ->where('status', true)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$seasonPrice) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No valid pricing found for the specified date range'
                ], 404);
            }

            // Calculate number of nights and days
            $startDateTime = new \DateTime($validated['start_date'] . ' ' . $validated['start_time']);
            $endDateTime = new \DateTime($validated['end_date'] . ' ' . $validated['end_time']);

            $numberOfNights = $this->calculateNights($startDateTime, $endDateTime);
            
            // Calculate exact days including partial days
            $interval = $startDateTime->diff($endDateTime);
            $numberOfDays = $interval->days;
            if ($interval->h > 0 || $interval->i > 0) {
                $numberOfDays += 1;
            }

            // Get configuration values
            $kmMin = $vehicleTypeConfig->km_min;
            $costPerKm = $seasonPrice->prix_km;

            // X value for night surcharge
            $nightSurchargeMultiplier = Config::where('key', 'night_surcharge_multiplier')->first()?->value ?? 150; 

            // Calculate base cost
            $kilometers = $validated['number_of_km'];
            $baseCost = 0;

            if ($kilometers < $kmMin) {
                // If kilometers are less than minimum
                if ($numberOfNights > 0) {
                    $baseCost = ($kmMin * $costPerKm) + ($costPerKm * $nightSurchargeMultiplier * $numberOfNights);
                } else {
                    $baseCost = $kmMin * $costPerKm;
                }
            } else {
                // If kilometers are greater than or equal to minimum
                if ($numberOfNights > 0) {
                    $baseCost = ($kilometers * $costPerKm) + ($costPerKm * $nightSurchargeMultiplier * $numberOfNights);
                } else {
                    $baseCost = $kilometers * $costPerKm;
                }
            }

            // Calculate final cost with TVA (assuming TVA is 19%)
            $tvaRate = 0.19;
            $tvaAmount = $baseCost * $tvaRate;
            $totalAmount = $baseCost + $tvaAmount;

            return response()->json([
                'status' => 'success',
                'data' => [
                    'base_amount' => round($baseCost, 2),
                    'tva_amount' => round($tvaAmount, 2),
                    'total_amount' => round($totalAmount, 2),
                    'price_per_km' => $costPerKm,
                    'total_km' => $kilometers,
                    'minimum_km' => $kmMin,
                    'number_of_nights' => $numberOfNights,
                    'number_of_days' => $numberOfDays,
                    'location_type' => $locationType->only(['id', 'nom_fr', 'nom_en', 'nom_ar']),
                    'vehicle_type' => $vehicleTypeConfig->typeVehicule->only(['id', 'nom_fr', 'nom_en', 'nom_ar']),
                    'season' => $seasonPrice->season->only(['id', 'nom_fr', 'nom_en', 'nom_ar', 'start_date', 'end_date'])
                ],
                'message' => 'Location amount calculated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error calculating location amount',
                'errors' => [
                    'system' => [$e->getMessage()]
                ]
            ], 500);
        }
    }

    private function calculateNights(\DateTime $start, \DateTime $end): int
    {
        $nights = 0;
        $current = clone $start;
        
        while ($current < $end) {
            $hour = (int)$current->format('H');
            // A night is counted if any part of 23:00-03:59 is included
            if ($hour >= 23 || $hour < 4) {
                $nights++;
                // Skip to the end of this night period (04:00)
                $current->modify('+1 day')->setTime(4, 0);
            } else {
                $current->modify('+1 hour');
            }
        }
        
        return $nights;
    }
}


