<?php

namespace App\Console\Commands;

use App\Models\Delegation;
use App\Models\Governorate;
use App\Models\Station;
use Illuminate\Console\Command;

class UpdateStationCoordinates extends Command
{
    protected $signature = 'stations:update-coordinates';
    protected $description = 'Update stations coordinates using OpenStreetMap API';

    public function handle()
    {
        $stations = Station::all();
        $bar = $this->output->createProgressBar($stations->count());

        $this->info('Starting to update station coordinates...');
        $this->newLine();

        foreach ($stations as $station) {
            $locationDetails = $this->getLocationDetails($station->nom_fr);

            $station->update([
                'longitude' => $locationDetails['longitude'],
                'latitude' => $locationDetails['latitude'],
                'id_delegation' => $this->findDelegation($locationDetails['delegation']),
                'id_governorate' => $this->findGovernorate($locationDetails['governorate'])
            ]);

            $this->line("Processed: {$station->nom_fr}");
            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);
        $this->info('All stations have been updated successfully!');

        // Show sample of updated stations
        $this->info('Sample of updated stations:');
        $this->table(
            ['Name', 'Longitude', 'Latitude', 'Delegation', 'Governorate'],
            Station::with(['delegation', 'governorate'])
                ->limit(5)
                ->get()
                ->map(fn($station) => [
                    $station->nom_fr,
                    $station->longitude,
                    $station->latitude,
                    $station->delegation?->name ?? null,
                    $station->governorate?->name ?? null
                ])
        );

        return Command::SUCCESS;
    }

    private function getLocationDetails($stationName)
    {
        // Add "Tunisia" to improve search accuracy
        $search = urlencode($stationName . ", Tunisia");
        $url = "https://nominatim.openstreetmap.org/search?q={$search}&format=json&limit=1&countrycodes=tn";

        // Add delay to respect API rate limits
        sleep(1);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'StationLocationUpdater/1.0');

        $response = curl_exec($ch);
        curl_close($ch);

        $data = json_decode($response, true);

        if (empty($data)) {
            return [
                'longitude' => 0,
                'latitude' => 0,
                'delegation' => null,
                'governorate' => null
            ];
        }

        $result = $data[0];
        
        // Parse display_name to extract delegation and governorate
        // Format: "City, Area, Délégation Name, Governorate, Postal, Tunisia"
        $addressParts = explode(', ', $result['display_name']);
        
        $delegation = null;
        $governorate = null;
        
        foreach ($addressParts as $part) {
            // Look for delegation (usually prefixed with "Délégation")
            if (str_contains(strtolower($part), 'délégation')) {
                $delegation = str_replace('Délégation ', '', $part);
            }
            // The governorate is usually the second-to-last part before the postal code
            else if (count($addressParts) >= 4 && !is_numeric($part) && $part !== 'Tunisia') {
                $governorate = $part;
            }
        }

        // If delegation not found in display_name, use the city name as delegation
        if (!$delegation && isset($result['name'])) {
            $delegation = $result['name'];
        }

        return [
            'longitude' => floatval($result['lon']),
            'latitude' => floatval($result['lat']),
            'delegation' => $delegation,
            'governorate' => $governorate
        ];
    }

    private function findDelegation($delegationName)
    {
        if (!$delegationName) {
            return null;
        }

        $delegation = Delegation::where(
            ['nom_fr' => $delegationName]
        )->first();

        return $delegation?->id ?? null;
    }

    private function findGovernorate($governorateName)
    {
        if (!$governorateName) {
            return null;
        }

        $governorate = Governorate::where(
            ['nom_fr' => $governorateName]
        )->first();

        return $governorate?->id ?? null;
    }
}

