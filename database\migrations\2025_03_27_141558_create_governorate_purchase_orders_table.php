<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('governorate_purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string("ref");
            $table->decimal("initial_amount", 10, 2);
            $table->decimal("current_amount", 10, 2);
            $table->boolean("status");
            $table->date("date");
            $table->foreignId('id_governorate')->constrained('governorates');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('governorate_purchase_orders');
    }
};