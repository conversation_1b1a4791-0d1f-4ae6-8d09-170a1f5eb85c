<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLanguage
{
    public function handle(Request $request, Closure $next)
    {
        $language = $request->header('Accept-Language', config('app.locale'));

        if (in_array($language, ['fr', 'ar', 'en'])) {
            App::setLocale($language);
        }

        return $next($request);
    }
}
