<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('type_vehicules', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('nom_fr');
            $table->string('nom_en')->nullable();
            $table->string('nom_ar')->nullable();
            $table->integer('nbre_max_place')->nullable();
            $table->string('swf')->nullable();
            $table->string('photo')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('type_vehicules');
    }
};
