<?php

namespace Database\Seeders;

use App\Models\Agency;
use App\Models\Delegation;
use App\Models\SalePoint;
use Illuminate\Database\Seeder;

class SalePointSeeder extends Seeder
{
    public function run(): void
    {
        $salePoints = [
            [
                'nom_fr' => 'Point de Vente Bab Alioua',
                'nom_en' => 'Bab Alioua Sale Point',
                'nom_ar' => 'نقطة بيع باب عليوة',
                'contact' => '+216 71 333 888',
                'address' => 'Station Bab Alioua, 1006 Tunis',
                'status' => true,
                'agency_code' => 'SNTRI-TUN',
                'delegation_name' => 'Bab El Bhar'
            ],
            [
                'nom_fr' => 'Point de Vente Sfax Centre',
                'nom_en' => 'Sfax Center Sale Point',
                'nom_ar' => 'نقطة بيع صفاقس المركز',
                'contact' => '+216 74 200 400',
                'address' => 'Centre Ville, 3000 Sfax',
                'status' => true,
                'agency_code' => 'SNTRI-SFX',
                'delegation_name' => 'Sfax Ville'
            ],
            [
                'nom_fr' => 'Point de Vente Sousse Medina',
                'nom_en' => 'Sousse Medina Sale Point',
                'nom_ar' => 'نقطة بيع سوسة المدينة',
                'contact' => '+216 73 225 600',
                'address' => 'Medina de Sousse, 4000 Sousse',
                'status' => true,
                'agency_code' => 'SNTRI-SOU',
                'delegation_name' => 'Sousse Ville'
            ],
            [
                'nom_fr' => 'Point de Vente République',
                'nom_en' => 'Republic Sale Point',
                'nom_ar' => 'نقطة بيع الجمهورية',
                'contact' => '+216 71 341 044',
                'address' => 'Avenue de la République, 1001 Tunis',
                'status' => true,
                'agency_code' => 'TUT-TUN-N',
                'delegation_name' => 'Bab El Bhar'
            ],
            [
                'nom_fr' => 'Point de Vente Ibn Khaldoun',
                'nom_en' => 'Ibn Khaldoun Sale Point',
                'nom_ar' => 'نقطة بيع ابن خلدون',
                'contact' => '+216 71 343 255',
                'address' => 'Station Ibn Khaldoun, 1002 Tunis',
                'status' => true,
                'agency_code' => 'TUT-TUN-S',
                'delegation_name' => 'Bab Bhar'
            ]
        ];

        foreach ($salePoints as $salePointData) {
            $agency = Agency::where('code', $salePointData['agency_code'])->first();
            $delegation = Delegation::where('nom_fr', $salePointData['delegation_name'])->first();
            
            if ($agency && $delegation) {
                SalePoint::create([
                    'nom_fr' => $salePointData['nom_fr'],
                    'nom_en' => $salePointData['nom_en'],
                    'nom_ar' => $salePointData['nom_ar'],
                    'contact' => $salePointData['contact'],
                    'address' => $salePointData['address'],
                    'status' => $salePointData['status'],
                    'id_delegation' => $delegation->id,
                    'id_governorate' => $delegation->id_governorate,
                    'id_agency' => $agency->id
                ]);
            }
        }
    }
}