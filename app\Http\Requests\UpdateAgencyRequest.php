<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAgencyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:50|unique:agencies,code,' . $this->agency->id,
            'contact' => 'sometimes|required|string|max:255|unique:agencies,contact,' . $this->agency->id,
            'address' => 'sometimes|required|string|max:255',
            'id_delegation' => 'sometimes|required|exists:delegations,id',
            'id_governorate' => 'sometimes|required|exists:governorates,id'
        ];
    }
}

