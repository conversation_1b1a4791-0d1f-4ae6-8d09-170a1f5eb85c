<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class SubsDuplication extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    protected $fillable = [
        'motif_duplicate_id',
        'subscription_id',
        'admin_id'
    ];

    /**
     * Get the subscription that owns the SubsDuplication
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    /**
     * Get the motif that owns the SubsDuplication
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function motif(): BelongsTo
    {
        return $this->belongsTo(MotifDuplicate::class, 'motif_duplicate_id', 'id');
    }

    /**
     * Get the admin that owns the SubsDuplication
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }
}
