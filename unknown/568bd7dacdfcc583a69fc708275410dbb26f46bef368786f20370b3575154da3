<?php

namespace App\Repositories;

use App\Models\Subscription;
use Prettus\Repository\Eloquent\BaseRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SubscriptionRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_subs_type' => '=',
        'client.identity_number' => 'like',
        'id_payment_method' => '=',
        'start_date' => '>=',
        'end_date' => '<=',
        'stage_date_start' => '>=',
        'stage_date_end' => '<=',
        'id_trip' => '=',
        'id_periodicity' => '=',
        'is_reversed' => '=',
        'is_social_affair' => '=',
        'is_printed' => '=',
        'status' => '=',
        'id_parent' => '='
    ];

    public function model(): string
    {
        return Subscription::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    public function create(array $attributes)
    {
        try {
            DB::beginTransaction();

            // Generate unique reference number
            $attributes['ref'] = 'SUB-' . time() . '-' . rand(1000, 9999);

            // Check if this is a renewal subscription and copy photo from parent
            if(isset($attributes['id_parent']) && $attributes['id_parent']) {
                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    $photo = $attributes['photo'];
                    $extension = $photo->getClientOriginalExtension();
                    if (empty($extension) || $photo->getClientOriginalName() === 'blob') {
                        $mimeType = $photo->getMimeType();
                        if (strpos($mimeType, 'jpeg') !== false) {
                            $extension = 'jpg';
                        } elseif (strpos($mimeType, 'png') !== false) {
                            $extension = 'png';
                        } elseif (strpos($mimeType, 'gif') !== false) {
                            $extension = 'gif';
                        } else {
                            $extension = 'jpg';
                        }
                    }
                    $filename = 'subscription_' . time() . '.' . $extension;
                    try {
                        if (!Storage::exists('public/subscriptions')) {
                            Storage::makeDirectory('public/subscriptions');
                        }
                        $path = $photo->storeAs('public/subscriptions', $filename);
                        if ($path) {
                            $attributes['photo'] = str_replace('public/', '', $path);
                        } else {
                            $content = file_get_contents($photo->getRealPath());
                            $manualPath = 'public/subscriptions/' . $filename;
                            $stored = Storage::put($manualPath, $content);
                            if ($stored) {
                                $attributes['photo'] = 'subscriptions/' . $filename;
                            } else {
                                unset($attributes['photo']);
                            }
                        }
                    } catch (\Exception $e) {
                        unset($attributes['photo']);
                    }
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        $parentSubscription = Subscription::find($attributes['id_parent']);
                        if ($parentSubscription && $parentSubscription->photo) {
                            $oldPhotoPath = 'public/' . $parentSubscription->photo;
                            if (Storage::exists($oldPhotoPath)) {
                                $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                                $newFilename = 'subscription_' . time() . '.' . $extension;
                                $newPath = 'subscriptions/' . $newFilename;

                                if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                    $attributes['photo'] = $newPath;
                                }
                            }
                        }
                    } else {
                        unset($attributes['photo']);

                        $parentSubscription = Subscription::find($attributes['id_parent']);
                        if ($parentSubscription && $parentSubscription->photo) {
                            $oldPhotoPath = 'public/' . $parentSubscription->photo;
                            if (Storage::exists($oldPhotoPath)) {
                                $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                                $newFilename = 'subscription_' . time() . '.' . $extension;
                                $newPath = 'subscriptions/' . $newFilename;

                                if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                    $attributes['photo'] = $newPath;
                                }
                            }
                        }
                    }
                } else {
                    $parentSubscription = Subscription::find($attributes['id_parent']);
                    if ($parentSubscription && $parentSubscription->photo) {
                        $oldPhotoPath = 'public/' . $parentSubscription->photo;
                        if (Storage::exists($oldPhotoPath)) {
                            $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                            $newFilename = 'subscription_' . time() . '.' . $extension;
                            $newPath = 'subscriptions/' . $newFilename;

                            if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                $attributes['photo'] = $newPath;
                            }
                        }
                    }
                }
            }

            // Handle photo upload or copying from parent subscription
            elseif (isset($attributes['photo'])) {
                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    // Handle uploaded file
                    $photo = $attributes['photo'];
                    $filename = 'subscription_' . time() . '.' . $photo->getClientOriginalExtension();
                    $path = $photo->storeAs('public/subscriptions', $filename);
                    // Extraire le chemin relatif sans le préfixe /storage/
                    $attributes['photo'] = str_replace('public/', '', $path);
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        // Handle base64 encoded image
                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        // C'est déjà une URL, on la laisse telle quelle
                        // Ne rien faire
                    } else {
                        // Chaîne non reconnue, on la supprime
                        unset($attributes['photo']);
                    }
                } else {
                    // Type non reconnu, on supprime la photo
                    unset($attributes['photo']);
                }
            }

            $subscription = parent::create($attributes);

            DB::commit();
            return $subscription->load(['subsType', 'client', 'paymentMethod', 'trip', 'periodicity', 'parentSubscription']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $attributes, $id)
    {
        try {
            DB::beginTransaction();

            $subscription = $this->find($id);

            // Handle photo upload
            if (isset($attributes['photo'])) {
                // Fonction pour supprimer l'ancienne photo
                $deleteOldPhoto = function() use ($subscription) {
                    if ($subscription->photo && Storage::exists('public/' . $subscription->photo)) {
                        Storage::delete('public/' . $subscription->photo);
                    }
                };

                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    // Handle uploaded file
                    $deleteOldPhoto();

                    $photo = $attributes['photo'];
                    $filename = 'subscription_' . time() . '.' . $photo->getClientOriginalExtension();
                    $path = $photo->storeAs('public/subscriptions', $filename);
                    // Extraire le chemin relatif sans le préfixe /storage/
                    $attributes['photo'] = str_replace('public/', '', $path);
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        $deleteOldPhoto();

                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        // Si c'est la même URL que celle déjà stockée, on ne fait rien
                        if ($attributes['photo'] === $subscription->photo) {
                            // Ne rien faire, garder la même photo
                            unset($attributes['photo']);
                        }
                    } else {
                        unset($attributes['photo']);
                    }
                } else {
                    unset($attributes['photo']);
                }
            }

            $subscription = parent::update($attributes, $id);

            DB::commit();
            return $subscription->load(['subsType', 'client', 'paymentMethod', 'trip', 'periodicity', 'parentSubscription']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $subscription = $this->find($id);

            // Delete photo if exists
            if ($subscription->photo && Storage::exists('public/' . $subscription->photo)) {
                Storage::delete('public/' . $subscription->photo);
            }

            $result = parent::delete($id);

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}



