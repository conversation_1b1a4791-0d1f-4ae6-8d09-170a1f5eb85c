<?php

namespace App\Services;

use App\Models\CardSequence;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CardSequenceTracker
{
    /**
     * Add cards to stock
     *
     * @param int $cardTypeId The card type ID
     * @param int $startSequence The start sequence number
     * @param int $endSequence The end sequence number
     * @return void
     */
    public function addToStock(int $cardTypeId, int $startSequence, int $endSequence): void
    {
        DB::beginTransaction();
        try {
            // Check for overlapping sequences
            $overlappingSequences = $this->findOverlappingSequences($cardTypeId, $startSequence, $endSequence);
            
            // If there are overlapping sequences, delete them
            if ($overlappingSequences->isNotEmpty()) {
                CardSequence::whereIn('id', $overlappingSequences->pluck('id'))->delete();
                
                // Create new sequences for any non-overlapping parts
                foreach ($overlappingSequences as $sequence) {
                    // If the sequence starts before our new sequence
                    if ($sequence->start_sequence < $startSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $sequence->start_sequence,
                            'end_sequence' => $startSequence - 1,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                    
                    // If the sequence ends after our new sequence
                    if ($sequence->end_sequence > $endSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $endSequence + 1,
                            'end_sequence' => $sequence->end_sequence,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                }
            }
            
            // Create the new available sequence
            CardSequence::create([
                'id_card_type' => $cardTypeId,
                'start_sequence' => $startSequence,
                'end_sequence' => $endSequence,
                'status' => 'available',
                'id_agent' => null,
            ]);
            
            // Merge consecutive sequences
            $this->mergeConsecutiveSequences($cardTypeId);
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error adding cards to stock: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Return cards from agent to stock
     *
     * @param int $cardTypeId The card type ID
     * @param int $startSequence The start sequence number
     * @param int $endSequence The end sequence number
     * @return void
     */
    public function returnToStock(int $cardTypeId, int $startSequence, int $endSequence): void
    {
        DB::beginTransaction();
        try {
            // Check for overlapping sequences
            $overlappingSequences = $this->findOverlappingSequences($cardTypeId, $startSequence, $endSequence);
            
            // If there are overlapping sequences, delete them
            if ($overlappingSequences->isNotEmpty()) {
                CardSequence::whereIn('id', $overlappingSequences->pluck('id'))->delete();
                
                // Create new sequences for any non-overlapping parts
                foreach ($overlappingSequences as $sequence) {
                    // If the sequence starts before our new sequence
                    if ($sequence->start_sequence < $startSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $sequence->start_sequence,
                            'end_sequence' => $startSequence - 1,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                    
                    // If the sequence ends after our new sequence
                    if ($sequence->end_sequence > $endSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $endSequence + 1,
                            'end_sequence' => $sequence->end_sequence,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                }
            }
            
            // Create the new available sequence
            CardSequence::create([
                'id_card_type' => $cardTypeId,
                'start_sequence' => $startSequence,
                'end_sequence' => $endSequence,
                'status' => 'available',
                'id_agent' => null,
            ]);
            
            // Merge consecutive sequences
            $this->mergeConsecutiveSequences($cardTypeId);
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error returning cards to stock: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Assign cards to agent
     *
     * @param int $cardTypeId The card type ID
     * @param int $startSequence The start sequence number
     * @param int $endSequence The end sequence number
     * @param int $agentId The agent ID
     * @return void
     */
    public function assignToAgent(int $cardTypeId, int $startSequence, int $endSequence, int $agentId): void
    {
        DB::beginTransaction();
        try {
            // Check for overlapping sequences
            $overlappingSequences = $this->findOverlappingSequences($cardTypeId, $startSequence, $endSequence);
            
            // If there are overlapping sequences, delete them
            if ($overlappingSequences->isNotEmpty()) {
                CardSequence::whereIn('id', $overlappingSequences->pluck('id'))->delete();
                
                // Create new sequences for any non-overlapping parts
                foreach ($overlappingSequences as $sequence) {
                    // If the sequence starts before our new sequence
                    if ($sequence->start_sequence < $startSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $sequence->start_sequence,
                            'end_sequence' => $startSequence - 1,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                    
                    // If the sequence ends after our new sequence
                    if ($sequence->end_sequence > $endSequence) {
                        CardSequence::create([
                            'id_card_type' => $cardTypeId,
                            'start_sequence' => $endSequence + 1,
                            'end_sequence' => $sequence->end_sequence,
                            'status' => $sequence->status,
                            'id_agent' => $sequence->id_agent,
                        ]);
                    }
                }
            }
            
            // Create the new occupied sequence
            CardSequence::create([
                'id_card_type' => $cardTypeId,
                'start_sequence' => $startSequence,
                'end_sequence' => $endSequence,
                'status' => 'occupied',
                'id_agent' => $agentId,
            ]);
            
            // Merge consecutive sequences
            $this->mergeConsecutiveSequences($cardTypeId);
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error assigning cards to agent: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Find overlapping sequences
     *
     * @param int $cardTypeId The card type ID
     * @param int $startSequence The start sequence number
     * @param int $endSequence The end sequence number
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function findOverlappingSequences(int $cardTypeId, int $startSequence, int $endSequence)
    {
        return CardSequence::where('id_card_type', $cardTypeId)
            ->where(function($query) use ($startSequence, $endSequence) {
                $query->where(function($q) use ($startSequence, $endSequence) {
                    $q->where('start_sequence', '<=', $startSequence)
                      ->where('end_sequence', '>=', $startSequence);
                })->orWhere(function($q) use ($startSequence, $endSequence) {
                    $q->where('start_sequence', '<=', $endSequence)
                      ->where('end_sequence', '>=', $endSequence);
                })->orWhere(function($q) use ($startSequence, $endSequence) {
                    $q->where('start_sequence', '>=', $startSequence)
                      ->where('end_sequence', '<=', $endSequence);
                });
            })
            ->get();
    }
    
    /**
     * Merge consecutive sequences of the same type
     *
     * @param int $cardTypeId The card type ID
     * @return void
     */
    private function mergeConsecutiveSequences(int $cardTypeId): void
    {
        // Merge available sequences
        $this->mergeAvailableSequences($cardTypeId);
        
        // Merge occupied sequences by agent
        $this->mergeOccupiedSequencesByAgent($cardTypeId);
    }
    
    /**
     * Merge consecutive available sequences
     *
     * @param int $cardTypeId The card type ID
     * @return void
     */
    private function mergeAvailableSequences(int $cardTypeId): void
    {
        // Get all available sequences for this card type, ordered by start_sequence
        $availableSequences = CardSequence::where('id_card_type', $cardTypeId)
            ->where('status', 'available')
            ->orderBy('start_sequence')
            ->get();
        
        if ($availableSequences->count() <= 1) {
            return;
        }
        
        $merged = false;
        $rangesToDelete = [];
        
        for ($i = 0; $i < $availableSequences->count() - 1; $i++) {
            $currentRange = $availableSequences[$i];
            $nextRange = $availableSequences[$i + 1];
            
            // Check if the ranges are consecutive
            if ($currentRange->end_sequence + 1 == $nextRange->start_sequence) {
                // Update the current range to include the next range
                $currentRange->end_sequence = $nextRange->end_sequence;
                $currentRange->save();
                
                // Mark the next range for deletion
                $rangesToDelete[] = $nextRange->id;
                
                // Update the collection
                $availableSequences[$i] = $currentRange;
                $availableSequences->forget($i + 1);
                $availableSequences = $availableSequences->values();
                
                $merged = true;
                $i--; // Recheck the current range with the next one
            }
        }
        
        // Delete the merged ranges
        if (!empty($rangesToDelete)) {
            CardSequence::whereIn('id', $rangesToDelete)->delete();
        }
    }
    
    /**
     * Merge consecutive occupied sequences by agent
     *
     * @param int $cardTypeId The card type ID
     * @return void
     */
    private function mergeOccupiedSequencesByAgent(int $cardTypeId): void
    {
        // Get all agents who have occupied sequences for this card type
        $agents = CardSequence::where('id_card_type', $cardTypeId)
            ->where('status', 'occupied')
            ->whereNotNull('id_agent')
            ->distinct()
            ->pluck('id_agent');
        
        foreach ($agents as $agentId) {
            // Get all occupied sequences for this card type and agent, ordered by start_sequence
            $occupiedSequences = CardSequence::where('id_card_type', $cardTypeId)
                ->where('status', 'occupied')
                ->where('id_agent', $agentId)
                ->orderBy('start_sequence')
                ->get();
            
            if ($occupiedSequences->count() <= 1) {
                continue;
            }
            
            $merged = false;
            $rangesToDelete = [];
            
            for ($i = 0; $i < $occupiedSequences->count() - 1; $i++) {
                $currentRange = $occupiedSequences[$i];
                $nextRange = $occupiedSequences[$i + 1];
                
                // Check if the ranges are consecutive
                if ($currentRange->end_sequence + 1 == $nextRange->start_sequence) {
                    // Update the current range to include the next range
                    $currentRange->end_sequence = $nextRange->end_sequence;
                    $currentRange->save();
                    
                    // Mark the next range for deletion
                    $rangesToDelete[] = $nextRange->id;
                    
                    // Update the collection
                    $occupiedSequences[$i] = $currentRange;
                    $occupiedSequences->forget($i + 1);
                    $occupiedSequences = $occupiedSequences->values();
                    
                    $merged = true;
                    $i--; // Recheck the current range with the next one
                }
            }
            
            // Delete the merged ranges
            if (!empty($rangesToDelete)) {
                CardSequence::whereIn('id', $rangesToDelete)->delete();
            }
        }
    }
}
