<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class CardType extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'code'
    ];

    public function motifDuplicates(): Has<PERSON>any
    {
        return $this->hasMany(MotifDuplicate::class, 'id_card_type');
    }

    public function subsCards(): HasMany
    {
        return $this->hasMany(SubsCard::class, 'id_card_type');
    }
    public function stockCards(): HasMany
    {
        return $this->hasMany(StockCard::class, 'id_card_type');
    }

    public function affectationCardTypes(): HasMany
    {
        return $this->hasMany(AffectationCardType::class, 'id_card_type');
    }
}
