<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use OwenIt\Auditing\Contracts\Auditable;

class PaymentMethod extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function affectationAgents(): BelongsToMany
    {
        return $this->belongsToMany(AffectationAgent::class, 'affectation_agent_payment_methods',
            'id_payment_method', 'id_affectation_agent')
            ->withTimestamps();
    }
}