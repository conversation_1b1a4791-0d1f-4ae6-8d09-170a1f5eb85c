<?php

return [
    'required' => 'Le champ :attribute est obligatoire.',
    'string' => 'Le champ :attribute doit être une chaîne de caractères.',
    'max' => [
    'string' => 'Le champ :attribute ne peut pas dépasser :max caractères.',
    'numeric' => 'Le champ :attribute ne peut pas être supérieur à :max.',
    ],
    'unique' => 'Le champ :attribute est déjà utilisé.',
    'email' => 'Le champ :attribute doit être une adresse email valide.',
    'regex' => 'Le format du champ :attribute est invalide.',
    'numeric' => 'Le champ :attribute doit être un nombre.',
    

    'attributes' => [
        'firstname' => 'prénom',
        'lastname' => 'nom de famille',
        'phone' => 'numéro de téléphone',
        'address' => 'adresse',
        'cin' => 'CIN',
        'email' => 'adresse email',
        'password' => 'mot de passe',
        'identity_number' => 'numéro d\'identité',
        'dob' => 'date de naissance',
        'code' => 'code',
        'age_max' => 'age max',
        'CODE_LINE' => 'code ligne',
        'name' => 'désignation',
        'commercial_speed' => 'vitesse commerciale',
        'type_service' => 'type de service',
        'status' => 'statut',
        'id_saison_location' => 'saison',
        'id_type_vehicule' => 'type de véhicule',
        'prix_km' => 'prix par km',
        'number_of_km' => 'nombre de km',
        'id_type_location' => 'type de location',
        'km_min' => 'km min',
        'ref' => 'référence',
        'amount' => 'montant',
        'date' => 'date',
    ],
];
