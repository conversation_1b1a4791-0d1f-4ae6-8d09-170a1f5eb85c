<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use OwenIt\Auditing\Contracts\Auditable;

class Client extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'lastname',
        'firstname',
        'society_name',
        'legal_representative',
        'dob',
        'phone',
        'identity_number',
        'address',
        'is_moral',
        'is_withTVA',
        'email',
        'password',
        'id_client_type',
        'id_delegation',
        'id_governorate',
        'id_establishment',
        'id_degree'
    ];

    protected $hidden = [
        'password'
    ];

    protected $casts = [
        'dob' => 'date'
    ];

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class, 'id_client');
    }

    public function clientType(): BelongsTo
    {
        return $this->belongsTo(TypeClient::class, 'id_client_type');
    }

    public function delegation(): BelongsTo
    {
        return $this->belongsTo(Delegation::class, 'id_delegation');
    }

    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'id_governorate');
    }

    public function establishment(): BelongsTo
    {
        return $this->belongsTo(Establishment::class, 'id_establishment');
    }

    public function degree(): BelongsTo
    {
        return $this->belongsTo(Degree::class, 'id_degree');
    }
}



