<?php

namespace Database\Seeders;

use App\Models\Degree;
use App\Models\TypeEstablishment;
use Illuminate\Database\Seeder;

class DegreeSeeder extends Seeder
{
    public function run(): void
    {
        $typeEstablishments = TypeEstablishment::all();
        
        if ($typeEstablishments->isEmpty()) {
            $this->command->info('No type establishments found. Please run TypeEstablishmentSeeder first.');
            return;
        }

        $degrees = [
            // Pour École Primaire
            [
                'nom_fr' => '1ère année primaire',
                'nom_en' => '1st grade',
                'nom_ar' => 'السنة الأولى ابتدائي',
                'age_max' => 7,
                'type_establishment' => 'École Primaire'
            ],
            [
                'nom_fr' => '6ème année primaire',
                'nom_en' => '6th grade',
                'nom_ar' => 'السنة السادسة ابتدائي',
                'age_max' => 12,
                'type_establishment' => 'École Primaire'
            ],
            
            // Pour Collège
            [
                'nom_fr' => '7ème année de base',
                'nom_en' => '7th grade',
                'nom_ar' => 'السنة السابعة أساسي',
                'age_max' => 13,
                'type_establishment' => 'Collège'
            ],
            [
                'nom_fr' => '9ème année de base',
                'nom_en' => '9th grade',
                'nom_ar' => 'السنة التاسعة أساسي',
                'age_max' => 16,
                'type_establishment' => 'Collège'
            ],
            
            // Pour Lycée
            [
                'nom_fr' => '1ère année secondaire',
                'nom_en' => '1st year secondary',
                'nom_ar' => 'السنة الأولى ثانوي',
                'age_max' => 17,
                'type_establishment' => 'Lycée'
            ],
            [
                'nom_fr' => 'Baccalauréat',
                'nom_en' => 'Baccalaureate',
                'nom_ar' => 'باكالوريا',
                'age_max' => 20,
                'type_establishment' => 'Lycée'
            ],
            
            // Pour Université
            [
                'nom_fr' => 'Licence',
                'nom_en' => 'Bachelor',
                'nom_ar' => 'إجازة',
                'age_max' => 23,
                'type_establishment' => 'Université'
            ],
            [
                'nom_fr' => 'Master',
                'nom_en' => 'Master',
                'nom_ar' => 'ماجستير',
                'age_max' => 25,
                'type_establishment' => 'Université'
            ],
            
            // Pour Institut Supérieur
            [
                'nom_fr' => 'BTS',
                'nom_en' => 'BTS',
                'nom_ar' => 'مؤهل التقني السامي',
                'age_max' => 21,
                'type_establishment' => 'Institut Supérieur'
            ],
            [
                'nom_fr' => 'Licence Professionnelle',
                'nom_en' => 'Professional Degree',
                'nom_ar' => 'إجازة مهنية',
                'age_max' => 23,
                'type_establishment' => 'Institut Supérieur'
            ]
        ];

        foreach ($degrees as $degree) {
            $typeEstablishment = TypeEstablishment::where('nom_fr', $degree['type_establishment'])->first();
            if ($typeEstablishment) {
                Degree::create([
                    'nom_fr' => $degree['nom_fr'],
                    'nom_en' => $degree['nom_en'],
                    'nom_ar' => $degree['nom_ar'],
                    'age_max' => $degree['age_max'],
                    'id_type_establishment' => $typeEstablishment->id
                ]);
            }
        }
    }
}