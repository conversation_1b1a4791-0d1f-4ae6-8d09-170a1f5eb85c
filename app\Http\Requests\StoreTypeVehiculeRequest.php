<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTypeVehiculeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'code' => 'required|string|max:50|unique:type_vehicules,code',
            'nbre_max_place' => 'nullable|integer|min:1',
            'swf' => 'nullable|string|max:255',
            'photo' => 'nullable|string|max:255',
            'status' => 'boolean'
        ];
    }
}
