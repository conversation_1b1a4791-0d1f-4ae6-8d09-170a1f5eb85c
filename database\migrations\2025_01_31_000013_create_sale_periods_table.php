<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sale_periods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('date_start_client');
            $table->date('date_end_client');
            $table->date('date_start_agent');
            $table->date('date_end_agent');
            $table->date('date_start_validity');
            $table->date('date_end_validity');
            $table->foreignId('id_periodicity')->constrained('periodicities');
            $table->foreignId('id_campaign')->constrained('campaigns');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sale_periods');
    }
};

