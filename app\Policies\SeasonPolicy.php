<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Season;
use Illuminate\Auth\Access\HandlesAuthorization;

class SeasonPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_seasons');
    }

    public function view(Admin $admin, Season $season): bool
    {
        return $admin->can('view_seasons');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_seasons');
    }

    public function update(Admin $admin, Season $season): bool
    {
        return $admin->can('edit_seasons');
    }

    public function delete(Admin $admin, Season $season): bool
    {
        return $admin->can('delete_seasons');
    }
}