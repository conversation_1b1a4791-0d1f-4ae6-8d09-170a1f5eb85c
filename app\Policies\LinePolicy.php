<?php

namespace App\Policies;

use App\Models\Line;
use App\Models\Admin;

class LinePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_lines');
    }

    public function view(Admin $admin, Line $line): bool
    {
        return $admin->hasPermissionTo('view_lines');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_lines');
    }

    public function update(Admin $admin, Line $line): bool
    {
        return $admin->hasPermissionTo('edit_lines');
    }

    public function delete(Admin $admin, Line $line): bool
    {
        return $admin->hasPermissionTo('delete_lines');
    }
}