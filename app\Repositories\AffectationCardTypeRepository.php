<?php

namespace App\Repositories;

use App\Models\AffectationCardType;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class AffectationCardTypeRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'start_serial_number' => '=',
        'end_serial_number' => '=',
        'id_card_type' => '=',
        'id_agent' => '='
    ];

    public function model(): string
    {
        return AffectationCardType::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}