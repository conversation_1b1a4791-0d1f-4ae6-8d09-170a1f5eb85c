<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subs_cards', function (Blueprint $table) {
            $table->id();
            $table->string('ref');
            $table->foreignId('id_card_type')->constrained('card_types');
            $table->foreignId('id_subscription')->constrained('subscriptions');
            $table->foreignId('id_motif_duplicate')->nullable()->constrained('motif_duplicates');
            $table->foreignId('id_sale_point')->nullable()->constrained('sale_points');
            $table->foreignId('id_affectation_card_type')->nullable()->constrained('affectation_card_types');
            $table->decimal("duplicate_amount", 10, 2)->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subs_cards');
    }
};

