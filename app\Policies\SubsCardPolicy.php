<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SubsCard;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubsCardPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, SubsCard $subsCard): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, SubsCard $subsCard): bool
    {
        return true;
    }

    public function delete(Admin $admin, SubsCard $subsCard): bool
    {
        return true;
    }
}