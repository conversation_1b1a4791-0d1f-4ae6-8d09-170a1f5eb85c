<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLocationTypeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:location_types,code,' . $this->location_type->id,
            'status' => 'required|boolean',
            'documents' => 'nullable|string|max:255'
        ];
    }
}