<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\WebsiteTrip;
use Illuminate\Auth\Access\HandlesAuthorization;

class WebsiteTripPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, WebsiteTrip $websiteTrip): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, WebsiteTrip $websiteTrip): bool
    {
        return true;
    }

    public function delete(Admin $admin, WebsiteTrip $websiteTrip): bool
    {
        return true;
    }
}