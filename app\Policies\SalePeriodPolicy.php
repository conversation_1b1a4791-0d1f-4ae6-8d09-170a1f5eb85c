<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SalePeriod;
use Illuminate\Auth\Access\HandlesAuthorization;

class SalePeriodPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_sales_periods');
    }

    public function view(Admin $admin, SalePeriod $salePeriod): bool
    {
        return $admin->can('view_sales_periods');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_sales_periods');
    }

    public function update(Admin $admin, SalePeriod $salePeriod): bool
    {
        return $admin->can('edit_sales_periods');
    }

    public function delete(Admin $admin, SalePeriod $salePeriod): bool
    {
        return $admin->can('delete_sales_periods');
    }
}