<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateConfigRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'key' => 'sometimes|string|max:255|unique:configs,key,' . $this->route('config')->id,
            'value' => 'nullable',
            'type' => 'sometimes|string|in:string,integer,float,boolean,json,array',
            'group' => 'nullable|string|max:255',
            'label_fr' => 'nullable|string|max:255',
            'label_en' => 'nullable|string|max:255',
            'label_ar' => 'nullable|string|max:255',
            'description_fr' => 'nullable|string',
            'description_en' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'is_public' => 'boolean',
            'is_system' => 'boolean'
        ];
    }
}
