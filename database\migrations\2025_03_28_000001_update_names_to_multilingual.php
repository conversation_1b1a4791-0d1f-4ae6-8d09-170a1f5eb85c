<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    protected $tables = [
        'seasons',
        'governorates',
        'agencies',
        'campaigns',
        'card_fees',
        'card_types',
        'degrees',
        'delegations',
        'establishments',
        'lines',
        'motif_duplicates',
        'sale_points',
        'sale_periods',
        'stations',
        'subs_types',
        'tariff_bases',
        'trips',
        'type_establishments',
        "payment_methods",
        "type_clients",
        "periodicities",
        "discounts",
        "options",
    ];

    public function up(): void
    {
        foreach ($this->tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->string('nom_fr')->after('name')->nullable();
                $table->string('nom_en')->nullable();
                $table->string('nom_ar')->nullable();
            });

            // Copy existing name values to nom_fr
            DB::statement("UPDATE `{$table}` SET nom_fr = name");

            Schema::table($table, function (Blueprint $table) {
                $table->dropColumn('name');
            });
        }
    }

    public function down(): void
    {
        foreach ($this->tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->string('name')->after('id')->nullable();
            });

            // Copy existing nom_fr values back to name
            DB::statement("UPDATE `{$table}` SET name = nom_fr");

            Schema::table($table, function (Blueprint $table) {
                $table->dropColumn(['nom_fr', 'nom_en', 'nom_ar']);
            });
        }
    }
};

