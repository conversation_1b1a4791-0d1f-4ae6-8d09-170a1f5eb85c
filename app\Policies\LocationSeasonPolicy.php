<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\LocationSeason;

class LocationSeasonPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_location_seasons');
    }

    public function view(Admin $admin, LocationSeason $locationSeason): bool
    {
        return $admin->hasPermissionTo('view_location_seasons');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_location_seasons');
    }

    public function update(Admin $admin, LocationSeason $locationSeason): bool
    {
        return $admin->hasPermissionTo('edit_location_seasons');
    }

    public function delete(Admin $admin, LocationSeason $locationSeason): bool
    {
        return $admin->hasPermissionTo('delete_location_seasons');
    }
}
