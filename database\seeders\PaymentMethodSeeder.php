<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    public function run(): void
    {
        $paymentMethods = [
            [
                'nom_fr' => 'Espèces',
                'nom_en' => 'Cash',
                'nom_ar' => 'نقدا',
                'status' => true
            ],
            [
                'nom_fr' => 'Carte Bancaire',
                'nom_en' => 'Credit Card',
                'nom_ar' => 'بطاقة ائتمان',
                'status' => true
            ],
            [
                'nom_fr' => 'Virement Bancaire',
                'nom_en' => 'Bank Transfer',
                'nom_ar' => 'تحويل مصرفي',
                'status' => true
            ],
            [
                'nom_fr' => 'Chèque',
                'nom_en' => 'Check',
                'nom_ar' => 'شيك',
                'status' => true
            ],
            [
                'nom_fr' => 'Paiement Mobile',
                'nom_en' => 'Mobile Payment',
                'nom_ar' => 'الدفع عبر الهاتف المحمول',
                'status' => true
            ]
        ];

        foreach ($paymentMethods as $paymentMethod) {
            PaymentMethod::create($paymentMethod);
        }
    }
}