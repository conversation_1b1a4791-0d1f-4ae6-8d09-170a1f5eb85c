<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateStockCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id_card_type' => 'sometimes|exists:card_types,id',
            'id_agent'     => 'nullable',
            'sequence_start' => 'sometimes|integer',
            'sequence_end'   => 'sometimes|integer',
            'mouvement'      => ['sometimes', 'string', Rule::in(['retour', 'ajout'])],
        ];
        if ($this->filled('id_agent')) {
            $rules['id_agent'] = 'exists:admins,id';
        }
    }
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->mouvement === 'ajout') {
                $this->validateAjout($validator);
            } elseif ($this->mouvement === 'retour') {
                $this->validateRetour($validator);
            }
        });
    }

    protected function validateAjout($validator)
    {
        $latestRecord = app('App\Repositories\StockCardRepository')
            ->getLatestRecord($this->id_card_type);

        if ($latestRecord && $this->sequence_start >= $latestRecord->sequence_end) {
            $validator->errors()->add(
                'sequence_start',
                'Pour un ajout, la séquence de début doit être supérieure à la dernière séquence existante (' . $latestRecord->sequence_end . ')'
            );
        }
    }

    protected function validateRetour($validator)
    {
        $stockCard = app('App\Repositories\StockCardRepository')
            ->findRecord($this->id_card_type, $this->sequence_start);

        if (
            !$stockCard ||
            $this->sequence_start < $stockCard->sequence_start ||
            $this->sequence_start > $stockCard->sequence_end ||
            $this->sequence_end > $stockCard->sequence_end
        ) {
            $validator->errors()->add(
                'sequence_start',
                'Pour un retour, les séquences doivent être comprises dans un intervalle existant'
            );
        }
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
