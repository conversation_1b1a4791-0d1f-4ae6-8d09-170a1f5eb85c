<?php

namespace App\Repositories;

use App\Models\TariffBase;
use Prettus\Repository\Eloquent\BaseRepository;

class TariffBaseRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'id_subs_type' => '=',
        'is_regular' => '=',
        'is_triff_fixed' => '=',
        'km_start' => '=',
        'km_end' => '='
    ];

    public function model(): string
    {
        return TariffBase::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}



