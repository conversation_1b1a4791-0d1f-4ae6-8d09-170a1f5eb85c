<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TariffBaseResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'id_subs_type' => $this->id_subs_type,
            'is_regular' => $this->is_regular,
            'is_triff_fixed' => $this->is_triff_fixed,
            'km_start' => $this->km_start,
            'km_end' => $this->km_end,
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'tariffs' => TariffResource::collection($this->whenLoaded('tariffs')),
            'tariff_options' => TariffOptionResource::collection($this->whenLoaded('tariffOptions')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}


