<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('type_vehicle_type_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_type_vehicule')->constrained('type_vehicules');
            $table->foreignId('id_type_location')->constrained('location_types');
            $table->integer('km_min')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();

            $table->unique(['id_type_vehicule', 'id_type_location'], 'vehicle_locations_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('type_vehicle_type_locations');
    }
};
