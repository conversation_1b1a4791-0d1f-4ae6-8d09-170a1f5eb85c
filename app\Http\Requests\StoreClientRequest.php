<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreClientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'lastname' => 'nullable|string|max:255',
            'firstname' => 'nullable|string|max:255',
            'society_name' => 'nullable|string|max:255',
            'legal_representative' => 'nullable|string|max:255',
            'dob' => 'nullable|date',
            'phone' => 'required|numeric',
            'identity_number' => 'required|string',
            'address' => 'required|string',
            'is_moral' => 'required|boolean',
            'is_withTVA' => 'nullable|boolean',
            'email' => 'nullable|email',
            'password' => 'nullable|sometimes|string',
            'id_client_type' => 'required|exists:type_clients,id',
            'id_delegation' => 'required|exists:delegations,id',
            'id_governorate' => 'required|exists:governorates,id',
            'id_establishment' => 'nullable|exists:establishments,id',
            'id_degree' => 'nullable|exists:degrees,id'
        ];
    }
}
