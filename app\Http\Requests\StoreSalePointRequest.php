<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSalePointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'contact' => 'required|string|max:255|unique:sale_points,contact',
            'address' => 'required|string|max:255',
            'status' => 'required|boolean',
            'id_delegation' => 'required|exists:delegations,id',
            'id_governorate' => 'required|exists:governorates,id',
            'id_agency' => 'required|exists:agencies,id'
        ];
    }
}