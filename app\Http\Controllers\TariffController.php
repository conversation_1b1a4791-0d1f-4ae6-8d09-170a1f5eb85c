<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTariffRequest;
use App\Http\Requests\UpdateTariffRequest;
use App\Http\Resources\TariffResource;
use App\Models\Tariff;
use App\Repositories\TariffRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TariffController extends Controller
{
    private TariffRepository $repository;

    public function __construct(TariffRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Tariff::class, 'tariff');
    }

    public function index(): AnonymousResourceCollection
    {
        return TariffResource::collection($this->repository->with('tariffBase')->latest()->paginate());
    }

    public function all(): AnonymousResourceCollection
    {
        return TariffResource::collection($this->repository->with('tariffBase')->all());
    }

    public function getByTariffBase($tariffBaseId): AnonymousResourceCollection
    {
        $tariffs = $this->repository->with('tariffBase')
            ->findWhere(['id_tariff_base' => $tariffBaseId]);
        return TariffResource::collection($tariffs);
    }

    public function store(StoreTariffRequest $request): JsonResponse
    {
        $tariff = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Tariff created successfully',
            'data' => new TariffResource($tariff)
        ], 201);
    }

    public function show(Tariff $tariff): TariffResource
    {
        $tariff->load('tariffBase');
        return new TariffResource($tariff);
    }

    public function update(UpdateTariffRequest $request, Tariff $tariff): JsonResponse
    {
        $tariff = $this->repository->update($request->validated(), $tariff->id);

        return response()->json([
            'message' => 'Tariff updated successfully',
            'data' => new TariffResource($tariff)
        ]);
    }

    public function destroy(Tariff $tariff): JsonResponse
    {
        $this->repository->delete($tariff->id);

        return response()->json([
            'message' => 'Tariff deleted successfully'
        ]);
    }
}
