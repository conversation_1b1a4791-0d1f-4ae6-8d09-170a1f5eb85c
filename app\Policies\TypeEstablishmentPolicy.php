<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TypeEstablishment;

class TypeEstablishmentPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_establishments');
    }

    public function view(Admin $admin, TypeEstablishment $typeEstablishment): bool
    {
        return $admin->hasPermissionTo('view_establishments');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_establishments');
    }

    public function update(Admin $admin, TypeEstablishment $typeEstablishment): bool
    {
        return $admin->hasPermissionTo('edit_establishments');
    }

    public function delete(Admin $admin, TypeEstablishment $typeEstablishment): bool
    {
        return $admin->hasPermissionTo('delete_establishments');
    }
}