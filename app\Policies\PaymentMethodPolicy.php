<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\PaymentMethod;

class PaymentMethodPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_payment_methods');
    }

    public function view(Admin $admin, PaymentMethod $paymentMethod): bool
    {
        return $admin->can('view_payment_methods');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_payment_methods');
    }

    public function update(Admin $admin, PaymentMethod $paymentMethod): bool
    {
        return $admin->can('edit_payment_methods');
    }

    public function delete(Admin $admin, PaymentMethod $paymentMethod): bool
    {
        return $admin->can('delete_payment_methods');
    }
}