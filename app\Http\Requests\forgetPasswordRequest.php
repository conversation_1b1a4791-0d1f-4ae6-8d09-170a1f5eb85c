<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class forgetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "email" => "required|string|email|exists:admins,email",
        ];
    }
    public function messages()
    {
        return [
            'email.required' => "L'adresse e-mail est requise.",
            'email.email' => "L'adresse e-mail doit être valide.",
            'email.exists' => "Aucun compte ne correspond à cette adresse e-mail.",
        ];
    }
}
