<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTypeClientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'color' => 'sometimes|required|string|max:7|regex:/^#[0-9A-F]{6}$/i',
            'is_student' => 'sometimes|required|boolean',
            'hasCIN' => 'sometimes|required|boolean',
            'is_impersonal' => 'sometimes|required|boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'nom_fr.required' => 'The French name is required.',
            'nom_en.required' => 'The English name is required.',
            'nom_ar.required' => 'The Arabic name is required.',
            'color.required' => 'The color is required.',
            'color.regex' => 'The color must be a valid hex color code.',
            'is_student.required' => 'Please specify if this type is for students.',
            'hasCIN.required' => 'Please specify if CIN is required.',
            'is_impersonal.required' => 'Please specify if this type is impersonal.'
        ];
    }
}

