<?php

namespace App\Imports;

use App\Models\SocialAffair;
use Maatwe<PERSON>ite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class SocialAffairsImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, WithBatchInserts, WithChunkReading
{
    use SkipsErrors;

    protected $governorate_id;
    protected $academic_year_id;

    public function __construct(int $governorate_id, ?int $academic_year_id = null)
    {
        $this->governorate_id = $governorate_id;
        $this->academic_year_id = $academic_year_id;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new SocialAffair([
            'governorate_id' => $this->governorate_id,
            'academic_year_id' => $this->academic_year_id,
            'delegation' => $row['delegation'] ?? '',
            'eleve_etudiant' => in_array(strtolower($row['eleve_etudiant'] ?? ''), ['eleve', 'etudiant']) ? strtolower($row['eleve_etudiant']) : null,
            'societe' => filter_var($row['societe'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'nom_parent' => $row['nom_parent'] ?? '',
            'cin_parent' => $row['cin_parent'] ?? '',
            'identifier' => $row['identifier'] ?? '',
            'dob' => isset($row['dob']) ? \Carbon\Carbon::parse($row['dob']) : null,
            'telephone' => $row['telephone'] ?? '',
            'nom_complet' => $row['nom_complet'] ?? '',
            'niveau_etude' => $row['niveau_etude'] ?? '',
            'trajet_requise' => $row['trajet_requise'] ?? '',
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'delegation' => 'required|string',
            'eleve_etudiant' => 'nullable|string|in:eleve,etudiant',
            'nom_parent' => 'required|string',
            'cin_parent' => 'required',
            'identifier' => 'required',
            'dob' => 'required|date',
            'telephone' => 'required',
            'nom_complet' => 'required|string',
            'niveau_etude' => 'required',
            'trajet_requise' => 'required|string',
        ];
    }

    /**
     * @return int
     */
    public function batchSize(): int
    {
        return 1000;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 1000;
    }
}
