<?php

namespace App\Http\Requests;
use App\Models\Admin;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'identifier' => 'required|string',
            'password' => 'required|string',
            'captcha' => 'required|string',
            'generatedCaptcha' => 'required|string',
        ];
    }

    protected function passedValidation()
    {
        // Vérification du captcha
        $captchaFromRequest = $this->input('captcha');
        $hashedCaptchaText = $this->input('generatedCaptcha');

    
        if (!Hash::check($captchaFromRequest, $hashedCaptchaText)) {
            throw ValidationException::withMessages([
                'captcha' => ['Captcha invalide'],
            ]);
        }

        // Vérification du mot de passe
        $identifier = $this->input('identifier');
        $password = $this->input('password');

        $admin = Admin::where('email', $identifier)
                      ->orWhere('phone', preg_replace('/^\+216-/', '', $identifier))
                      ->orWhere('cin', $identifier)
                      ->first();

        if (!$admin || !Hash::check($password, $admin->password)) {
            throw ValidationException::withMessages([
                'identifier' => ['Identifiants incorrects'],
            ]);
        }

        // Facultatif : stocker l'utilisateur pour utilisation ultérieure dans le contrôleur
        $this->merge(['admin' => $admin]);
    }

}
