<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTripRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'line_ids' => 'sometimes|required|array|min:1',
            'line_ids.*' => 'required|exists:lines,id',
            'stations' => 'sometimes|required|array',
            'stations.id_station_start' => 'required_with:stations|exists:stations,id',
            'stations.id_station_end' => 'required_with:stations|exists:stations,id|different:stations.id_station_start',
            'status' => 'sometimes|required|boolean',
            'inter_station' => 'sometimes|required|boolean',
            'is_aller_retour' => 'sometimes|nullable|boolean',
            'number_of_km' => 'sometimes|required|numeric|min:0|max:999999.99',

            // Tariff options validation
            'tariff_options' => 'sometimes|required|array',
            'tariff_options.*.id_subs_type' => 'required|exists:subs_types,id',
            'tariff_options.*.is_regular' => 'required|boolean',
            'tariff_options.*.id_tariff_base' => 'nullable|exists:tariff_bases,id',
            'tariff_options.*.manual_tariff' => 'nullable|numeric|min:0'
        ];
    }
}

