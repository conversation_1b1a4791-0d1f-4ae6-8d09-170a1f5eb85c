<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TypeClient;
use Illuminate\Auth\Access\HandlesAuthorization;

class TypeClientPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_client_types');
    }

    public function view(Admin $admin, TypeClient $typeClient): bool
    {
        return $admin->can('view_client_types');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_client_types');
    }

    public function update(Admin $admin, TypeClient $typeClient): bool
    {
        return $admin->can('edit_client_types');
    }

    public function delete(Admin $admin, TypeClient $typeClient): bool
    {
        return $admin->can('delete_client_types');
    }
}