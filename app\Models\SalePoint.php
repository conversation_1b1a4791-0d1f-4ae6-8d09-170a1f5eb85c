<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class SalePoint extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'contact',
        'address',
        'status',
        'id_delegation',
        'id_governorate',
        'id_agency'
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * Get the delegation that owns the sale point.
     */
    public function delegation(): BelongsTo
    {
        return $this->belongsTo(Delegation::class, 'id_delegation');
    }

    /**
     * Get the governorate that owns the sale point.
     */
    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'id_governorate');
    }

    /**
     * Get the agency that owns the sale point.
     */
    public function agency(): BelongsTo
    {
        return $this->belongsTo(Agency::class, 'id_agency');
    }

    /**
     * Get the subscription cards for the sale point.
     */
    public function subsCards(): HasMany
    {
        return $this->hasMany(SubsCard::class, 'id_sale_point');
    }

    /**
     * Get the agent affectations for the sale point.
     */
    public function affectationAgents(): HasMany
    {
        return $this->hasMany(AffectationAgent::class, 'id_sale_point');
    }
}



