<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class TariffOption extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'id_trip',
        'id_subs_type',
        'is_regular',
        'id_tariff_base',
        'manual_tariff',
    ];

    protected $casts = [
        'is_regular' => 'boolean',
        'manual_tariff' => 'decimal:3',
    ];

    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class, 'id_trip');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }

    public function tariffBase(): BelongsTo
    {
        return $this->belongsTo(TariffBase::class, 'id_tariff_base');
    }
}