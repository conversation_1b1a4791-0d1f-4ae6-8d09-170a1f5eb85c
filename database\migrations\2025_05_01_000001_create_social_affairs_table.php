<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('social_affairs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('governorate_id')->constrained('governorates');
            $table->string('delegation');
            $table->enum('eleve_etudiant', ['eleve', 'etudiant'])->nullable();
            $table->string('societe')->nullable();
            $table->string('nom_parent');
            $table->string('cin_parent')->nullable();
            $table->string('identifier')->nullable();
            $table->date('dob')->nullable();
            $table->string('telephone');
            $table->string('nom_complet');
            $table->string('niveau_etude');
            $table->string('trajet_requise');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('social_affairs');
    }
};

