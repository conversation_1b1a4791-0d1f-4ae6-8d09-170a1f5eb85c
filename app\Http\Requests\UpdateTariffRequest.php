<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTariffRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_tariff_base' => 'required|exists:tariff_bases,id',
            'tariff' => 'required|numeric|min:0',
            'date_subscription' => 'required|date',
            'date_website' => 'required|date'
        ];
    }
}
