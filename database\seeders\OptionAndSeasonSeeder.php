<?php

namespace Database\Seeders;

use App\Models\Option;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OptionAndSeasonSeeder extends Seeder
{
    public function run(): void
    {
        $options = [
            [
                'id' => 1,
                'nom_fr' => 'Supprimer dimanche',
                'nom_en' => 'Remove Sunday',
                'nom_ar' => 'حذف الأحد'
            ],
            [
                'id' => 2,
                'nom_fr' => 'ete seulement',
                'nom_en' => 'Summer only',
                'nom_ar' => 'الصيف فقط'
            ],
            [
                'id' => 3,
                'nom_fr' => 'Dimanche ramadhan',
                'nom_en' => 'Ramadan Sunday',
                'nom_ar' => 'أحد رمضان'
            ],
            [
                'id' => 4,
                'nom_fr' => 'Supprimer ramadhan',
                'nom_en' => 'Remove Ramadan',
                'nom_ar' => 'حذف رمضان'
            ],
            [
                'id' => 5,
                'nom_fr' => 'ramadhan seulement',
                'nom_en' => 'Ramadan only',
                'nom_ar' => 'رمضان فقط'
            ],
            [
                'id' => 6,
                'nom_fr' => 'dimanche seulement',
                'nom_en' => 'Sunday only',
                'nom_ar' => 'الأحد فقط'
            ],
            [
                'id' => 7,
                'nom_fr' => 'Dimanche ete',
                'nom_en' => 'Summer Sunday',
                'nom_ar' => 'أحد الصيف'
            ],
            [
                'id' => 8,
                'nom_fr' => 'Supprimer ete',
                'nom_en' => 'Remove Summer',
                'nom_ar' => 'حذف الصيف'
            ],
            [
                'id' => 9,
                'nom_fr' => 'ete/supprimer dimanche',
                'nom_en' => 'Summer/Remove Sunday',
                'nom_ar' => 'الصيف/حذف الأحد'
            ],
            [
                'id' => 10,
                'nom_fr' => 'Supprimer ramdhan/supprimer dimanche',
                'nom_en' => 'Remove Ramadan/Remove Sunday',
                'nom_ar' => 'حذف رمضان/حذف الأحد'
            ],
            [
                'id' => 11,
                'nom_fr' => 'Ramadhan/supprimer dimanche',
                'nom_en' => 'Ramadan/Remove Sunday',
                'nom_ar' => 'رمضان/حذف الأحد'
            ],
            [
                'id' => 12,
                'nom_fr' => 'ete dimanche/supprimer dimanche',
                'nom_en' => 'Summer Sunday/Remove Sunday',
                'nom_ar' => 'أحد الصيف/حذف الأحد'
            ],
            [
                'id' => 13,
                'nom_fr' => 'supprimer ete/supprimer dimanche',
                'nom_en' => 'Remove Summer/Remove Sunday',
                'nom_ar' => 'حذف الصيف/حذف الأحد'
            ],
            [
                'id' => 14,
                'nom_fr' => 'supprimer ramadhan/dimanche seulement',
                'nom_en' => 'Remove Ramadan/Sunday only',
                'nom_ar' => 'حذف رمضان/الأحد فقط'
            ],
            [
                'id' => 15,
                'nom_fr' => 'ramdhan/dimanche seulement',
                'nom_en' => 'Ramadan/Sunday only',
                'nom_ar' => 'رمضان/الأحد فقط'
            ],
            [
                'id' => 16,
                'nom_fr' => 'Supprimer ramadhan/supprimer dimanche',
                'nom_en' => 'Remove Ramadan/Remove Sunday',
                'nom_ar' => 'حذف رمضان/حذف الأحد'
            ],
            [
                'id' => 17,
                'nom_fr' => 'ete/ramadhan/supprimer dimanche',
                'nom_en' => 'Summer/Ramadan/Remove Sunday',
                'nom_ar' => 'الصيف/رمضان/حذف الأحد'
            ],
            [
                'id' => 18,
                'nom_fr' => 'supprimer ete/supprimer ramdhan',
                'nom_en' => 'Remove Summer/Remove Ramadan',
                'nom_ar' => 'حذف الصيف/حذف رمضان'
            ],
            [
                'id' => 19,
                'nom_fr' => 'Toujours',
                'nom_en' => 'Always',
                'nom_ar' => 'دائما'
            ],
            [
                'id' => 20,
                'nom_fr' => 'Supprimer Samedi et Dimanche',
                'nom_en' => 'Remove Saturday and Sunday',
                'nom_ar' => 'حذف السبت والأحد'
            ],
            [
                'id' => 21,
                'nom_fr' => 'Supprimer Ramadhan',
                'nom_en' => 'Remove Ramadan',
                'nom_ar' => 'حذف رمضان'
            ]
        ];

        foreach ($options as $option) {
            Option::updateOrCreate(['id' => $option['id']], $option);
        }

        $optionSeasonRelations = [
            ['CODE_OPTION' => '1', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '1', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '1', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '2', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '3', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '4', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '4', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '5', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '6', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '6', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '6', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '7', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '8', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '9', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '10', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '10', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '11', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '12', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '13', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '13', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '14', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '14', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '15', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '16', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '16', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '17', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '17', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '18', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '19', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '19', 'CODE_SAISON' => '2'],
            ['CODE_OPTION' => '19', 'CODE_SAISON' => '3'],
            ['CODE_OPTION' => '21', 'CODE_SAISON' => '1'],
            ['CODE_OPTION' => '21', 'CODE_SAISON' => '2']
        ];

        DB::table('option_seasons')->truncate();

        foreach ($optionSeasonRelations as $relation) {
            DB::table('option_seasons')->insert([
                'option_id' => $relation['CODE_OPTION'],
                'season_id' => $relation['CODE_SAISON'],
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }
}
