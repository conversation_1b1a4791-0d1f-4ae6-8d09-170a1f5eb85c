<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePeriodicityRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'periodicity_code' => 'sometimes|required|string|max:255|unique:periodicities,periodicity_code,' . $this->periodicity->id,
            'max_days_per_week' => 'sometimes|required|integer|min:1|max:7'
        ];
    }
}
