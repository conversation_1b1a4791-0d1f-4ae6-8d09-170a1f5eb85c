<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class Subscription extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'id_subs_type',
        'id_client',
        'id_payment_method',
        'start_date',
        'end_date',
        'id_trip',
        'is_reversed',
        'id_periodicity',
        'photo',
        'is_social_affair',
        'hasVacances',
        'rest_days',
        'subs_number',
        'status',
        'id_parent',
        'renewal_date',
        'id_agent',
        'is_stagiaire',
        'id_subscriber',
        'id_affectation',
        'id_sale_point',
        'id_sale_period',
        'special_client',
        'stage_date_start',
        'stage_date_end',
        'is_printed',
        'ref',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_reversed' => 'boolean',
        'is_social_affair' => 'boolean',
        'hasVacances' => 'boolean',
        'rest_days' => 'array',
        'subs_number' => 'integer',
        'renewal_date' => 'date',
        'special_client' => 'string',
        'stage_date_start' => 'date',
        'stage_date_end' => 'date',
        'is_printed' => 'boolean'
    ];

    public function subsCards(): HasMany
    {
        return $this->hasMany(SubsCard::class, 'id_subscription');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'id_client');
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'id_payment_method');
    }

    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class, 'id_trip');
    }

    public function periodicity(): BelongsTo
    {
        return $this->belongsTo(Periodicity::class, 'id_periodicity');
    }

    public function parentSubscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'id_parent');
    }

    public function childSubscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'id_parent');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'subscription_id');
    }


    public function duplications()
    {
        return $this->hasMany(SubsDuplication::class, 'subscription_id');
    }
}


