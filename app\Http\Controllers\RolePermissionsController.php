<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\Permission;
use App\Models\Role;
use App\Repositories\RoleRepository;
use Illuminate\Http\Request;

class RolePermissionsController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(RoleRepository::class);
    }

    /*|--------------------------------------------------------------------------
    |  FETCH ALL ROLES WITHOUT PAGINATION
    |-------------------------------------------------------------------------- */
    public function all()
    {
        try {
            $admins = Role::all();
            return response()->json([
                'success' => true,
                'data' => $admins
            ]);
        }catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    /*|--------------------------------------------------------------------------
    |  FETCH ROLES WITH PAGINATION
    |-------------------------------------------------------------------------- */
    public function index(Request $request)
    {
        try {
            $roles = $this->repository->orderBy('created_at', 'desc')
                ->paginate($request->input('perPage') ?? config('roles.pagination'));
            return $roles;
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    /*|--------------------------------------------------------------------------
   |  STORE ROLE
   |-------------------------------------------------------------------------- */
    public function store(Request $request)
    {
        try {
            $role = new Role();
            $role->name = $request->input('name');
            $role->guard_name= 'api';
            $role->save();

            return response()->json([
                'message' => 'Role created successfully.',
                'data' => $role,
            ], 201);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

    /*|--------------------------------------------------------------------------
    |  UPDATE ROLE
    |-------------------------------------------------------------------------- */
    public function update(Request $request, $id)
    {
        try {
            $role = Role::findOrFail($id);

            $role->name = $request->name ?? $role->name;

            $role->save();

            return response()->json([
                'message' => 'Role updated successfully.',
                'data' => $role,
            ], 200);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

    /*|--------------------------------------------------------------------------
    |  DESTROY ROLE
    |-------------------------------------------------------------------------- */
    public function destroy($id)
    {
        try {
            $role = Role::find($id);

            if (!$role) {
                return response()->json(['error' => 'Role not found.'], 404);
            }

            $role->delete();
            return response()->json(['message' => $role], 200);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

   /*|--------------------------------------------------------------------------
   |  FETCH ASSIGNED & UNASSIGNED PERMISSION FOR A ROLE
   |-------------------------------------------------------------------------- */
    public function getAssignedUnassignedPermissionsForRole($roleId)
    {
        $role = Role::findOrFail($roleId);

       $assignedPermissions = $role->permissions->map(function ($permission) {
           return [
               'id' => $permission->id,
               'name' => $permission->name,
               'category_name' => optional($permission->category)->name ?? 'Uncategorized',
           ];
       });

       $unassignedPermissions = Permission::whereNotIn('id', $role->permissions->pluck('id'))
           ->get()
           ->map(function ($permission) {
               return [
                   'id' => $permission->id,
                   'name' => $permission->name,
                   'category_name' => optional($permission->category)->name ?? 'Uncategorized',
               ];
           });

        return response()->json([
            'assigned' => $assignedPermissions,
            'unassigned' => $unassignedPermissions,
        ]);
    }

    /*|--------------------------------------------------------------------------
   |  FETCH ASSIGNED & UNASSIGNED ROLES FOR ADMIN
   |-------------------------------------------------------------------------- */
    public function getAssignedUnassignedRolesForAdmin($id)
    {
        $admin = Admin::find($id);

        if (!$admin) {
            return response()->json(['message' => 'admin not found'], 404);
        }

        $allRoles = Role::all();
        $assignedRoles = $admin->roles;
        $unassignedRoles = $allRoles->diff($assignedRoles);
        return response()->json([
            'assigned' => $assignedRoles,
            'unassigned' => $unassignedRoles
        ]);
    }


   /*|--------------------------------------------------------------------------
   |  ASSIGN PERMISSIONS TO A ROLE
   |-------------------------------------------------------------------------- */
    public function assignPermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissionIds' => 'required|array',
            'permissionIds.*' => 'exists:permissions,id',
        ]);

        $role->permissions()->attach($request->permissionIds);

        return response()->json(['message' => 'Permissions assignées avec succès.'], 200);
    }

   /*|--------------------------------------------------------------------------
   |  REMOVE PERMISSIONS FROM A ROLE
   |-------------------------------------------------------------------------- */
    public function removePermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissionIds' => 'required|array',
            'permissionIds.*' => 'exists:permissions,id',
        ]);

        $role->permissions()->detach($request->permissionIds);

        return response()->json(['message' => 'Permissions supprimées avec succès.'], 200);
    }

    /*|--------------------------------------------------------------------------
    |  ASSIGN ROLES TO AN ADMIN
    |-------------------------------------------------------------------------- */
    public function assignRolesToUser(Request $request, $adminId)
    {
        $request->validate([
            'roleIds' => 'required|array',
            'roleIds.*' => 'exists:roles,id',
        ]);

        $admin = Admin::findOrFail($adminId);
        $admin->roles()->attach($request->roleIds);  // Assigner les rôles à l'utilisateur

        return response()->json(['message' => 'Rôles assignés avec succès.']);
    }

    /*|--------------------------------------------------------------------------
   |  REMOVE ROLES FROM AN ADMIN
   |-------------------------------------------------------------------------- */
    public function removeRolesFromUser(Request $request, $adminId)
    {
        $request->validate([
            'roleIds' => 'required|array',
            'roleIds.*' => 'exists:roles,id',
        ]);

        $admin = Admin::findOrFail($adminId);
        $admin->roles()->detach($request->roleIds);  // Retirer les rôles de l'utilisateur

        return response()->json(['message' => 'Rôles retirés avec succès.']);
    }
}
