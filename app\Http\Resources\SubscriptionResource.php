<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\TripResource;
use App\Http\Resources\PeriodicityResource;
use App\Http\Resources\SubsTypeResource;
use App\Http\Resources\ClientResource;
use App\Http\Resources\PaymentMethodResource;
use App\Http\Resources\SubsCardResource;
use App\Http\Resources\TransactionResource;

class SubscriptionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_subs_type' => $this->id_subs_type,
            'id_client' => $this->id_client,
            'id_agent' => $this->id_agent,
            'id_subscriber' => $this->id_subscriber,
            'is_stagiaire' => $this->is_stagiaire,
            'id_payment_method' => $this->id_payment_method,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'id_trip' => $this->id_trip,
            'id_periodicity' => $this->id_periodicity,
            'is_reversed' => $this->is_reversed,
            'photo' => $this->photo,
            'is_social_affair' => $this->is_social_affair,
            'hasVacances' => $this->hasVacances,
            'rest_days' => $this->rest_days,
            'subs_number' => $this->subs_number,
            'status' => $this->status,
            'id_parent' => $this->id_parent,
            'renewal_date' => $this->renewal_date,
            'special_client' => $this->special_client,
            'stage_date_start' => $this->stage_date_start,
            'stage_date_end' => $this->stage_date_end,
            'is_printed' => $this->is_printed,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relations
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'client' => new ClientResource($this->whenLoaded('client')),
            'payment_method' => new PaymentMethodResource($this->whenLoaded('paymentMethod')),
            'trip' => new TripResource($this->whenLoaded('trip')),
            'periodicity' => new PeriodicityResource($this->whenLoaded('periodicity')),
            'parent_subscription' => new SubscriptionResource($this->whenLoaded('parentSubscription')),
            'child_subscriptions' => SubscriptionResource::collection($this->whenLoaded('childSubscriptions')),
            'subs_cards' => SubsCardResource::collection($this->whenLoaded('subsCards')),
            'transactions' => TransactionResource::collection($this->whenLoaded('transactions')),
            'latestTransaction' => new TransactionResource($this->whenLoaded('latestTransaction')),
        ];
    }
}

