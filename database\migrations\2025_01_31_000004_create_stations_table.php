<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('stations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('longitude')->default("");
            $table->string('latitude')->default("");
            $table->foreignId('id_delegation')->nullable()->constrained('delegations');
            $table->foreignId('id_governorate')->nullable()->constrained('governorates');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stations');
    }
};

