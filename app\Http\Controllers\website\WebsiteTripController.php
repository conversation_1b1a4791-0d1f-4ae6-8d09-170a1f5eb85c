<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Http\Resources\SeasonResource;
use App\Http\Resources\StationResource;
use App\Models\Season;
use App\Models\Station;
use App\Models\WebsiteTrip;
use App\Repositories\SeasonRepository;
use App\Repositories\StationRepository;
use App\Repositories\WebsiteTripRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Models\LineStation;
use App\Models\TariffBase;

class WebsiteTripController extends Controller
{
    private WebsiteTripRepository $websiteTripRepository;
    private StationRepository $stationRepository;

    private SeasonRepository $seasonRepository;

    public function __construct(
        WebsiteTripRepository $websiteTripRepository, 
        StationRepository $stationRepository,
        SeasonRepository $seasonRepository
        )
    {
        $this->websiteTripRepository = $websiteTripRepository;
        $this->stationRepository = $stationRepository;
        $this->seasonRepository = $seasonRepository;
    }

    public function stations_all(): AnonymousResourceCollection
    {
        return StationResource::collection(
            $this->stationRepository->with(['delegation', 'governorate'])->all()
        );
    }

    public function seasons_all(): AnonymousResourceCollection
    {
        return SeasonResource::collection(
            $this->seasonRepository->all()
        );
    }

    public function relatedStations(Station $station): JsonResponse
    {
        $station->load(['delegation', 'governorate']);
        
        // First, get all lines that contain the selected station
        $lineIds = LineStation::where('id_station', $station->id)
            ->pluck('id_line');

        // Then, get all stations from these lines (except the selected station)
        $relatedStations = Station::whereHas('lineStations', function($query) use ($lineIds) {
            $query->whereIn('id_line', $lineIds);
        })
        ->where('id', '!=', $station->id)
        ->with(['delegation', 'governorate'])
        ->get();

        return response()->json([
            'selected_station' => new StationResource($station),
            'related_stations' => StationResource::collection($relatedStations)
        ]);
    }

    public function getTripsByStations(Station $startStation, Station $endStation, Season $season): JsonResponse
    {
        // Load necessary relationships
        $startStation->load(['delegation', 'governorate']);
        $endStation->load(['delegation', 'governorate']);

        // Get the website tariff base
        $websiteTariff = TariffBase::where('for_website', true)
            ->orderBy('date', 'desc')
            ->first();

        // Get website trips that match the start and end stations
        $trips = WebsiteTrip::where(function($query) use ($startStation, $endStation) {
            $query->where(function($q) use ($startStation, $endStation) {
                $q->where('id_station_start', $startStation->id)
                  ->where('id_station_end', $endStation->id)
                  ->where('status', true);
            })->orWhere(function($q) use ($startStation, $endStation) {
                $q->where('id_station_start', $endStation->id)
                  ->where('id_station_end', $startStation->id)
                  ->where('status', true);
            });
        })
        ->with(['line' => function($query) {
            $query->with(['lineStations' => function($query) {
                $query->orderBy('position')
                ->with(['station']);
            }]);
        }, 'startStation', 'endStation'])
        ->get();
        
        $tripsWithTimes = $trips->map(function($trip) use ($season, $startStation, $endStation, $websiteTariff) {
            $commercialSpeed = $trip->line->commercial_speed;
            $amount = $websiteTariff ? $trip->number_of_km * $websiteTariff->tariffPerKM : 0;
            $lineStations = $trip->line->lineStations;

            // Determine si le trajet est dans le sens normal ou inverse
            $isForwardDirection = $trip->id_station_start === $startStation->id;

            // Trouver la station de départ sélectionnée dans les stations de la ligne
            $selectedStartStation = $lineStations->first(function($station) use ($startStation) {
                return $station->station->id === $startStation->id;
            });

            // Trouver la station d'arrivée sélectionnée dans les stations de la ligne
            $selectedEndStation = $lineStations->first(function($station) use ($endStation) {
                return $station->station->id === $endStation->id;
            });

            // Déterminer le terminus de départ selon la direction
            $startTerminus = $isForwardDirection 
                ? $lineStations->first() // Premier terminus si direction normale
                : $lineStations->last(); // Dernier terminus si direction inverse

            $departureTimes = [];
            
            if ($startTerminus && isset($startTerminus->start_time[$season->id])) {
                $baseTimes = $startTerminus->start_time[$season->id];
                
                // Calculer la distance totale entre le terminus et la station de départ
                $totalDistance = 0;
                
                if ($isForwardDirection) {
                    // Direction normale: du premier terminus vers la station de départ
                    $positions = range(
                        min($startTerminus->position, $selectedStartStation->position),
                        max($startTerminus->position, $selectedStartStation->position) - 1
                    );
                } else {
                    // Direction inverse: du dernier terminus vers la station de départ
                    $positions = range(
                        min($startTerminus->position, $selectedStartStation->position),
                        max($startTerminus->position, $selectedStartStation->position) - 1
                    );
                }

                // Calculer la distance totale
                foreach ($positions as $pos) {
                    $currentStation = $lineStations->firstWhere('position', $pos);
                    $nextStation = $lineStations->firstWhere('position', $pos + 1);
                    
                    if ($currentStation && $nextStation) {
                        $routeTrip = $trip->line->trips->first(function($t) use ($currentStation, $nextStation) {
                            return ($t->startStation->id === $currentStation->station->id && 
                                   $t->endStation->id === $nextStation->station->id) ||
                                   ($t->startStation->id === $nextStation->station->id && 
                                    $t->endStation->id === $currentStation->station->id);
                        });
                        
                        if ($routeTrip) {
                            $totalDistance += $routeTrip->number_of_km;
                        }
                    }
                }

                // Calculer le temps à ajouter/soustraire
                $timeToAdd = round(($totalDistance / $commercialSpeed) * 60);

                // Calculer les horaires de départ
                foreach ($baseTimes as $baseTime) {
                    if ($isForwardDirection) {
                        if ($selectedStartStation->position > $startTerminus->position) {
                            $departureTimes[] = $this->addMinutesToTime($baseTime, $timeToAdd);
                        } else {
                            $departureTimes[] = $this->addMinutesToTime($baseTime, -$timeToAdd);
                        }
                    } else {
                        if ($selectedStartStation->position > $startTerminus->position) {
                            $departureTimes[] = $this->addMinutesToTime($baseTime, -$timeToAdd);
                        } else {
                            $departureTimes[] = $this->addMinutesToTime($baseTime, $timeToAdd);
                        }
                    }
                }

                sort($departureTimes); // Trier les horaires
            }

            // Calculer la durée totale du trajet
            $totalTripDistance = 1;
            $tripPositions = range(
                min($selectedStartStation?->position, $selectedEndStation?->position),
                max($selectedStartStation?->position, $selectedEndStation?->position) - 1
            );



            foreach ($tripPositions as $pos) {
                $currentStation = $lineStations->firstWhere('position', $pos);
                $nextStation = $lineStations->firstWhere('position', $pos + 1);
                
                if ($currentStation && $nextStation) {
                    $routeTrip = $trip->line->trips->first(function($t) use ($currentStation, $nextStation) {
                        return ($t->startStation->id === $currentStation->station->id && 
                               $t->endStation->id === $nextStation->station->id) ||
                               ($t->startStation->id === $nextStation->station->id && 
                                $t->endStation->id === $currentStation->station->id);
                    });
                    
                    if ($routeTrip) {
                        $totalTripDistance += $routeTrip->number_of_km;
                    }
                }
            }

            $durationInHours = $totalTripDistance / $commercialSpeed;
            $durationInMinutes = round($durationInHours * 60);
            
            // Convertir en format heures:minutes
            $hours = floor($durationInMinutes / 60);
            $minutes = $durationInMinutes % 60;
            $duration = sprintf("%02d:%02d", $hours, $minutes);

            return [
                'id' => $trip->id,
                'status' => $trip->status,
                'trip_code' => $trip->code,
                'number_of_km' => $totalTripDistance,
                'start_station' => [
                    'id' => $startStation->id,
                    'nom_fr' => $startStation->nom_fr,
                    'nom_ar' => $startStation->nom_ar,
                    'nom_en' => $startStation->nom_en,
                ],
                'end_station' => [
                    'id' => $endStation->id,
                    'nom_fr' => $endStation->nom_fr,
                    'nom_ar' => $endStation->nom_ar,
                    'nom_en' => $endStation->nom_en,
                ],
                'line' => [
                    'id' => $trip->line->id,
                    'code_line' => $trip->line->CODE_LINE,
                    'nom_fr' => $trip->line->nom_fr,
                    'nom_ar' => $trip->line->nom_ar,
                    'nom_en' => $trip->line->nom_en,
                    'type_service' => $trip->line->type_service,
                ],
                'departure_times' => $departureTimes,
                'duration' => $duration,   
                'amount' => $amount
            ];
        });

        return response()->json([
            'trips' => $tripsWithTimes
        ]);
    }

    private function addMinutesToTime(string $time, int $minutes): string
    {
        $timestamp = strtotime($time);
        return date('H:i', $timestamp + ($minutes * 60));
    }
}






























