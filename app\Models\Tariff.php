<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class Tariff extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    
    protected $fillable = [
        'id_tariff_base',
        'tariff',
        'date_subscription',
        'date_website',
    ];

    protected $casts = [
        'tariff' => 'decimal:3',
        'date_subscription' => 'date',
        'date_website' => 'date',
    ];

    public function tariffBase(): BelongsTo
    {
        return $this->belongsTo(TariffBase::class, 'id_tariff_base');
    }
}
