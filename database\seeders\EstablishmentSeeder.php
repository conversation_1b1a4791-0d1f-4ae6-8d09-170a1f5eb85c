<?php

namespace Database\Seeders;

use App\Models\Delegation;
use App\Models\Establishment;
use App\Models\TypeEstablishment;
use Illuminate\Database\Seeder;

class EstablishmentSeeder extends Seeder
{
    public function run(): void
    {
        $delegations = Delegation::all();
        $typeEstablishments = TypeEstablishment::all();

        if ($delegations->isEmpty() || $typeEstablishments->isEmpty()) {
            $this->command->info('No delegations or type establishments found. Please run GovernorateDelegationSeeder and TypeEstablishmentSeeder first.');
            return;
        }

        $establishments = [
            [
                'nom_fr' => 'École Primaire Ibn Khaldoun',
                'nom_en' => 'Ibn Khaldoun Primary School',
                'nom_ar' => 'مدرسة ابن خلدون الابتدائية',
                'abbreviation' => 'EPIK',
                'cnss_etab' => '12345678',
                'delegation_name' => 'La Marsa',
                'type_establishment_name' => 'École Primaire'
            ],
            [
                'nom_fr' => 'Lycée Pilote de l\'Ariana',
                'nom_en' => 'Ariana Pilot High School',
                'nom_ar' => 'معهد أريانة النموذجي',
                'abbreviation' => 'LPA',
                'cnss_etab' => '23456789',
                'delegation_name' => 'Ariana Ville',
                'type_establishment_name' => 'Lycée'
            ],
            [
                'nom_fr' => 'Université de Tunis El Manar',
                'nom_en' => 'University of Tunis El Manar',
                'nom_ar' => 'جامعة تونس المنار',
                'abbreviation' => 'UTM',
                'cnss_etab' => '34567890',
                'delegation_name' => 'Tunis',
                'type_establishment_name' => 'Université'
            ],
            [
                'nom_fr' => 'Institut Supérieur des Études Technologiques',
                'nom_en' => 'Higher Institute of Technological Studies',
                'nom_ar' => 'المعهد العالي للدراسات التكنولوجية',
                'abbreviation' => 'ISET',
                'cnss_etab' => '45678901',
                'delegation_name' => 'Sfax Ville',
                'type_establishment_name' => 'Institut Supérieur'
            ]
        ];

        foreach ($establishments as $establishmentData) {
            $delegation = Delegation::where('nom_fr', $establishmentData['delegation_name'])->first();
            $typeEstablishment = TypeEstablishment::where('nom_fr', $establishmentData['type_establishment_name'])->first();

            if ($delegation && $typeEstablishment) {
                Establishment::create([
                    'nom_fr' => $establishmentData['nom_fr'],
                    'nom_en' => $establishmentData['nom_en'],
                    'nom_ar' => $establishmentData['nom_ar'],
                    'abbreviation' => $establishmentData['abbreviation'],
                    'cnss_etab' => $establishmentData['cnss_etab'],
                    'id_delegation' => $delegation->id,
                    'id_type_establishment' => $typeEstablishment->id
                ]);
            }
        }
    }
}

