<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TypeVehiculeSaisonLocation;

class TypeVehiculeSaisonLocationPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_vehicle_season_pricing');
    }

    public function view(Admin $admin, TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): bool
    {
        return $admin->hasPermissionTo('view_vehicle_season_pricing');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_vehicle_season_pricing');
    }

    public function update(Admin $admin, TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): bool
    {
        return $admin->hasPermissionTo('edit_vehicle_season_pricing');
    }

    public function delete(Admin $admin, TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): bool
    {
        return $admin->hasPermissionTo('delete_vehicle_season_pricing');
    }
}
