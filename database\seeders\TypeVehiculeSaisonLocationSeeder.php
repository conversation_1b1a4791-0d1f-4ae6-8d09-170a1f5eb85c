<?php

namespace Database\Seeders;

use App\Models\LocationSeason;
use App\Models\TypeVehicule;
use App\Models\TypeVehiculeSaisonLocation;
use Illuminate\Database\Seeder;

class TypeVehiculeSaisonLocationSeeder extends Seeder
{
    public function run(): void
    {
        // Get all type vehicules
        $typeVehicules = TypeVehicule::all();

        // Get all location seasons
        $seasons = LocationSeason::all();

        // Create relationships with random prix_km values
        foreach ($typeVehicules as $typeVehicule) {
            foreach ($seasons as $season) {
                TypeVehiculeSaisonLocation::create([
                    'id_type_vehicule' => $typeVehicule->id,
                    'id_saison_location' => $season->id,
                    'prix_km' => rand(100, max: 500),
                    'status' => true
                ]);
            }
        }
    }
}
