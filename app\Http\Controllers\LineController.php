<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLineRequest;
use App\Http\Requests\UpdateLineRequest;
use App\Http\Requests\StoreLineStationAssignmentRequest;
use App\Http\Requests\UpdateLineStationAssignmentRequest;
use App\Http\Resources\LineResource;
use App\Models\Line;
use App\Models\Option;
use App\Models\Station;
use App\Models\SubsType;
use App\Models\Trip;
use App\Repositories\LineRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LineController extends Controller
{
    private LineRepository $repository;

    public function __construct(LineRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Line::class, 'line');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return LineResource::collection(
            $this->repository
                        ->latest()
                        ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return LineResource::collection($this->repository->where('status', 1)->get());
    }

    public function store(StoreLineRequest $request): JsonResponse
    {
        $line = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Line created successfully',
            'data' => new LineResource($line)
        ], 201);
    }

    public function show(Line $line): LineResource
    {
        return new LineResource($line);
    }

    public function update(UpdateLineRequest $request, Line $line): JsonResponse
    {
        $line = $this->repository->update($request->validated(), $line->id);

        return response()->json([
            'message' => 'Line updated successfully',
            'data' => new LineResource($line)
        ]);
    }

    public function destroy(Line $line): JsonResponse
    {
        try {
            DB::beginTransaction();

            foreach ($line->trips as $trip) {
                $trip->tariffOptions()->delete();
            }

            // Supprimer les trips
            $line->trips()->delete();

            // Supprimer les website_trips
            $line->websiteTrips()->delete();

            // Supprimer les line_stations
            $line->lineStations()->delete();

            // Supprimer la ligne
            $this->repository->delete($line->id);

            DB::commit();

            return response()->json([
                'message' => 'Line deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to delete line',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getStationsAndRoutes(Line $line): JsonResponse
    {
        try {
            $options = Option::with('seasons')->get()->keyBy('id');

            $line->load([
                'lineStations' => function ($query) {
                    $query->orderBy('position');
                },
                'lineStations.station',
                'trips' => function ($query) {
                    $query->where('inter_station', true)
                        ->with(['startStation', 'endStation']);
                }
            ]);

            $formattedLine = [
                'id' => $line->id,
                'code_line' => $line->CODE_LINE,
                'nom_fr' => $line->nom_fr,
                'nom_ar' => $line->nom_ar,
                'nom_en' => $line->nom_en,
                'service_type' => strtoupper($line->type_service),
                'status' => $line->status ? 'ACTIF' : 'INACTIF',
                'created_at' => $line->created_at,
                'commercial_speed' => $line->commercial_speed,
                'stations' => $line->lineStations->map(function ($lineStation) use ($options) {
                    $departureConfigs = collect($lineStation->departure_configs ?? [])->map(function ($config) use ($options) {
                        $option = $options->get($config['option']);
                        return array_merge($config, [
                            'option_details' => $option ? [
                                'id' => $option->id,
                                'nom_fr' => $option->nom_fr,
                                'nom_en' => $option->nom_en,
                                'nom_ar' => $option->nom_ar,
                                'seasons' => $option->seasons->map(function ($season) {
                                    return [
                                        'id' => $season->id,
                                        'nom_fr' => $season->nom_fr,
                                        'nom_en' => $season->nom_en,
                                        'nom_ar' => $season->nom_ar,
                                        'start_date' => $season->start_date,
                                        'end_date' => $season->end_date,
                                        'priority' => $season->priority
                                    ];
                                })->values()->all()
                            ] : null
                        ]);
                    })->values()->all();

                    return [
                        'id' => $lineStation->station->id,
                        'nom_fr' => $lineStation->station->nom_fr,
                        'nom_ar' => $lineStation->station->nom_ar,
                        'nom_en' => $lineStation->station->nom_en,
                        'position' => $lineStation->position,
                        'type' => $lineStation->type,
                        'has_departures' => $lineStation->has_departures,
                        'departure_configs' => $departureConfigs
                    ];
                })->values()->all(),
                'routes' => $line->trips->map(function ($trip) {
                    return [
                        'id' => $trip->id,
                        'number_of_km' => $trip->number_of_km,
                        'station_depart' => [
                            'id' => $trip->startStation->id,
                            'nom_fr' => $trip->startStation->nom_fr,
                            'nom_ar' => $trip->startStation->nom_ar,
                            'nom_er' => $trip->startStation->nom_er,
                        ],
                        'station_arrival' => [
                            'id' => $trip->endStation->id,
                            'nom_fr' => $trip->endStation->nom_fr,
                            'nom_ar' => $trip->endStation->nom_ar,
                            'nom_en' => $trip->endStation->nom_en,
                        ],
                        'inter_station' => $trip->inter_station,
                        'status' => $trip->status
                    ];
                })->values()->all()
            ];

            return response()->json($formattedLine);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function assignStations(StoreLineStationAssignmentRequest $request): JsonResponse
    {
        try {
            \DB::beginTransaction();

            $line = Line::findOrFail($request->id_line);

            $line->lineStations()->delete();

            // delete only trips with inter_station = true that are associated with this line
            $tripsToDelete = $line->trips()->where('inter_station', true)->get();
            foreach ($tripsToDelete as $trip) {
                $trip->lines()->detach($line->id);
                // If trip has no more lines, delete it completely
                if ($trip->lines()->count() == 0) {
                    $trip->delete();
                }
            }

            foreach ($request->validated()['stations'] as $stationData) {
                $line->lineStations()->create([
                    'id_station' => $stationData['id_station'],
                    'position' => $stationData['position'],
                    'type' => $stationData['type'],
                    'has_departures' => $stationData['has_departures'],
                    'departure_configs' => $stationData['departure_configs'] ?? []
                ]);
            }

            foreach ($request->validated()['routes'] as $routeData) {
                $trip = Trip::create([
                    'nom_fr' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_en' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_ar' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'id_station_start' => $routeData['id_station_start'],
                    'id_station_end' => $routeData['id_station_end'],
                    'status' => $routeData['status'],
                    'inter_station' => $routeData['inter_station'],
                    'number_of_km' => $routeData['number_of_km']
                ]);

                // Attach the trip to the line
                $trip->lines()->attach($line->id);
            }

            \DB::commit();
            return response()->json([
                'message' => 'Stations and routes assigned successfully',
                'data' => new LineResource($line->load(['lineStations.station', 'trips']))
            ]);
        }
        catch (\Exception $e) {
            \DB::rollBack();
            return response()->json([
                'message' => 'Failed to assign stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateStationsAndRoutes(UpdateLineStationAssignmentRequest $request): JsonResponse
    {
        try {
            \DB::beginTransaction();

            $line = Line::findOrFail($request->id_line);

            $line->lineStations()->delete();

            // delete only trips with inter_station = true that are associated with this line
            $tripsToDelete = $line->trips()->where('inter_station', true)->get();
            foreach ($tripsToDelete as $trip) {
                $trip->lines()->detach($line->id);
                // If trip has no more lines, delete it completely
                if ($trip->lines()->count() == 0) {
                    $trip->delete();
                }
            }

            foreach ($request->validated()['stations'] as $stationData) {
                $line->lineStations()->create([
                    'id_station' => $stationData['id_station'],
                    'position' => $stationData['position'],
                    'type' => $stationData['type'],
                    'has_departures' => $stationData['has_departures'],
                    'departure_configs' => $stationData['departure_configs'] ?? []
                ]);
            }

            foreach ($request->validated()['routes'] as $routeData) {
                $trip = Trip::create([
                    'nom_fr' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_en' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_ar' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'id_station_start' => $routeData['id_station_start'],
                    'id_station_end' => $routeData['id_station_end'],
                    'status' => $routeData['status'],
                    'inter_station' => $routeData['inter_station'],
                    'number_of_km' => $routeData['number_of_km']
                ]);

                // Attach the trip to the line
                $trip->lines()->attach($line->id);
            }

            \DB::commit();
            return response()->json([
                'message' => 'Stations and routes updated successfully',
                'data' => new LineResource($line->load(['lineStations.station', 'trips']))
            ]);
        }
        catch (\Exception $e) {
            \DB::rollBack();
            return response()->json([
                'message' => 'Failed to update stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getLinesByTrip(int $tripId): AnonymousResourceCollection
    {
        Trip::findOrFail($tripId);

        $lines = Line::whereHas('trips', function($query) use ($tripId) {
                $query->where('id', $tripId);
            })
            ->get();

        return LineResource::collection($lines);
    }

    public function getLinesByStations(int $departureStationId, int $arrivalStationId, int $subsTypeId): JsonResponse
    {
        // Validate that stations and subscription type exist
        Station::findOrFail($departureStationId);
        Station::findOrFail($arrivalStationId);
        SubsType::findOrFail($subsTypeId);

        // Find lines that contain trips matching the criteria
        $lines = Line::whereHas('trips', function($query) use ($departureStationId, $arrivalStationId, $subsTypeId) {
            $query->where('inter_station', false)
                  ->where(function($tripQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                // Direct direction: departure -> arrival
                $tripQuery->where(function($directQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                    $directQuery->where('id_station_start', $departureStationId)
                               ->where('id_station_end', $arrivalStationId)
                               ->whereHas('tariffOptions', function($tariffQuery) use ($subsTypeId) {
                                   $tariffQuery->where('id_subs_type', $subsTypeId);
                               });
                })
                // If is_aller_retour is true, also consider reverse direction
                ->orWhere(function($reverseQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                    $reverseQuery->where('id_station_start', $arrivalStationId)
                                ->where('id_station_end', $departureStationId)
                                ->where('is_aller_retour', true)
                                ->whereHas('tariffOptions', function($tariffQuery) use ($subsTypeId) {
                                    $tariffQuery->where('id_subs_type', $subsTypeId);
                                });
                });
            });
        })
        ->with(['trips' => function($query) use ($departureStationId, $arrivalStationId, $subsTypeId) {
            $query->where('inter_station', false)
                  ->where(function($tripQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                $tripQuery->where(function($directQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                    $directQuery->where('id_station_start', $departureStationId)
                               ->where('id_station_end', $arrivalStationId)
                               ->whereHas('tariffOptions', function($tariffQuery) use ($subsTypeId) {
                                   $tariffQuery->where('id_subs_type', $subsTypeId);
                               });
                })
                ->orWhere(function($reverseQuery) use ($departureStationId, $arrivalStationId, $subsTypeId) {
                    $reverseQuery->where('id_station_start', $arrivalStationId)
                                ->where('id_station_end', $departureStationId)
                                ->where('is_aller_retour', true)
                                ->whereHas('tariffOptions', function($tariffQuery) use ($subsTypeId) {
                                    $tariffQuery->where('id_subs_type', $subsTypeId);
                                });
                });
            })
            ->with(['startStation', 'endStation', 'tariffOptions']);
        }])
        ->get();

        return response()->json([
            'data' => LineResource::collection($lines)
        ]);
    }

    public function getLinesConfigByStations(int $departureStationId, int $arrivalStationId): JsonResponse
    {
        Station::findOrFail($departureStationId);
        Station::findOrFail($arrivalStationId);

        $lines = Line::whereHas('lineStations', function($query) use ($departureStationId) {
                $query->where('id_station', $departureStationId);
            })
            ->whereHas('lineStations', function($query) use ($arrivalStationId) {
                $query->where('id_station', $arrivalStationId);
            })
            ->get();

        return response()->json([
            'data' => LineResource::collection($lines)
        ]);
    }
}

