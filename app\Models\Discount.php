<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use OwenIt\Auditing\Contracts\Auditable;

class Discount extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'percentage',
        'is_stagiaire',
        'special_client',
        'id_subs_type',
        'date_start',
        'date_end'
    ];

    protected $casts = [
        'percentage' => 'decimal:2',
        'id_subs_type' => 'integer',
        'is_stagiaire' => 'boolean',
        'special_client' => 'string',
        'date_start' => 'date',
        'date_end' => 'date',
    ];

    public function periodicities(): BelongsToMany
    {
        return $this->belongsToMany(Periodicity::class, 'discount_periodicity', 'discount_id', 'periodicity_id');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }
}

