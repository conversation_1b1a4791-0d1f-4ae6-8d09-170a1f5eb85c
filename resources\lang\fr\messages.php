<?php

return [
    'card_type' => [
        'delete' => [
            'error' => 'Impossible de supprimer ce type de carte car il est utilisé dans des affectations ou des abonnements',
            'success' => 'Type de carte supprimé avec succès'
        ]
    ],
    'client' => [
        'has_cin' => [
            'not_found' => 'Aucun client trouvé',
            'found' => 'Client récupéré avec succès'
        ]
        ],
    "subscription" => [
        'affectation_not_found' => "L'agent doit être affecté à un point de vente et à une période de vente pour créer un abonnement",
        'duplicated_subscription' => "Ce client a déjà un abonnement scolaire ou universitaire dans la période de vente actuelle.",
        "duplicated_subscription_short" => "Abonnement scolaire ou universitaire existant",
        'invalid_trip_tariff_options' => "Le trajet sélectionné n'a pas d'options tarifaires pour ce type d'abonnement."
    ]
];

