<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCampaignRequest;
use App\Http\Requests\UpdateCampaignRequest;
use App\Http\Resources\CampaignResource;
use App\Models\Campaign;
use App\Repositories\CampaignRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class CampaignController extends Controller
{
    private CampaignRepository $repository;

    public function __construct(CampaignRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Campaign::class, 'campaign');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return CampaignResource::collection(
            $this->repository
                        ->with(['salePeriods', 'abnType'])
                        ->latest()
                        ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return CampaignResource::collection(
            $this->repository->with(['salePeriods', 'abnType'])->all()
        );
    }

    public function store(StoreCampaignRequest $request): JsonResponse
    {
        $campaign = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Campaign created successfully',
            'data' => new CampaignResource($campaign->load(['salePeriods', 'abnType']))
        ], 201);
    }

    public function show(Campaign $campaign): CampaignResource
    {
        return new CampaignResource($campaign->load(['salePeriods', 'abnType']));
    }

    public function update(UpdateCampaignRequest $request, Campaign $campaign): JsonResponse
    {
        $campaign = $this->repository->update($request->validated(), $campaign->id);

        return response()->json([
            'message' => 'Campaign updated successfully',
            'data' => new CampaignResource($campaign->load(['salePeriods', 'abnType']))
        ]);
    }

    public function destroy(Campaign $campaign): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete associated sale periods
            $campaign->salePeriods()->delete();

            // delete the campaign
            $this->repository->delete($campaign->id);

            DB::commit();

            return response()->json([
                'message' => 'Campaign deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}

