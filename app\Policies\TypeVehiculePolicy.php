<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TypeVehicule;

class TypeVehiculePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_type_vehicules');
    }

    public function view(Admin $admin, TypeVehicule $typeVehicule): bool
    {
        return $admin->hasPermissionTo('view_type_vehicules');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_type_vehicules');
    }

    public function update(Admin $admin, TypeVehicule $typeVehicule): bool
    {
        return $admin->hasPermissionTo('edit_type_vehicules');
    }

    public function delete(Admin $admin, TypeVehicule $typeVehicule): bool
    {
        return $admin->hasPermissionTo('delete_type_vehicules');
    }
}
