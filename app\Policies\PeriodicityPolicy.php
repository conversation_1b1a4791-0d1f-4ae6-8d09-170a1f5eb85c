<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Periodicity;
use Illuminate\Auth\Access\HandlesAuthorization;

class PeriodicityPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_periodicities');
    }

    public function view(Admin $admin, Periodicity $periodicity): bool
    {
        return $admin->hasPermissionTo('create_periodicities');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_periodicities');
    }

    public function update(Admin $admin, Periodicity $periodicity): bool
    {
        return $admin->hasPermissionTo('edit_periodicities');
    }

    public function delete(Admin $admin, Periodicity $periodicity): bool
    {
        return $admin->hasPermissionTo('delete_periodicities');
    }
}