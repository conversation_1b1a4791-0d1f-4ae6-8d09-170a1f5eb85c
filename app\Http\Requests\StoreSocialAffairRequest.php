<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSocialAffairRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'governorate_id' => 'required|exists:governorates,id',
            'academic_year_id' => 'nullable|exists:academic_years,id',
            'delegation' => 'required|string|max:255',
            'eleve_etudiant' => 'required|string|in:eleve,etudiant',
            'societe' => 'required|string|max:255',
            'nom_parent' => 'required|string|max:255',
            'cin_parent' => 'required|string|max:255',
            'identifier' => 'nullable|string|max:255',
            'dob' => 'nullable|date',
            'telephone' => 'required|string|max:255',
            'nom_complet' => 'required|string|max:255',
            'niveau_etude' => 'required|string|max:255',
            'trajet_requise' => 'required|string|max:255'
        ];
    }
}
