<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Discount;
use Illuminate\Auth\Access\HandlesAuthorization;

class DiscountPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('view_discounts');
    }

    public function view(Admin $admin, Discount $discount): bool
    {
        return $admin->can('view_discounts');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_discounts');
    }

    public function update(Admin $admin, Discount $discount): bool
    {
        return $admin->can('edit_discounts');
    }

    public function delete(Admin $admin, Discount $discount): bool
    {
        return $admin->can('delete_discounts');
    }
}