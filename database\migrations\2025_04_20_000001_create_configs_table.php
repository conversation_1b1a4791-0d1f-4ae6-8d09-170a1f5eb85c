<?php

use App\Models\Config;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('configs', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, integer, boolean, json, etc.
            $table->string('group')->nullable();
            $table->string('label_fr')->nullable();
            $table->string('label_en')->nullable();
            $table->string('label_ar')->nullable();
            $table->text('description_fr')->nullable();
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->boolean('is_public')->default(false); 
            $table->boolean('is_system')->default(false); 
            $table->timestamps();
        });
        
        // Ajouter des configurations par défaut si nécessaire
        Config::create(['key' => 'night_surcharge_multiplier', 'value' => 150, 'type' => 'integer', 'is_public' => true]);
        Config::create(['key' => 'majoration', 'value' => '1.5', 'type' => 'string', 'is_public' => true]);
    }

    

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('configs');
    }
};
