<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['id_line']);
            // Then drop the column
            $table->dropColumn('id_line');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Add the column back
            $table->foreignId('id_line')->after('nom_ar')->constrained('lines');
        });
    }
};
