<?php

namespace Database\Seeders;

use App\Models\LocationType;
use Illuminate\Database\Seeder;

class LocationTypeSeeder extends Seeder
{
    public function run(): void
    {
        $locationTypes = [
            [
                'nom_fr' => 'Mariage/association Sportive',
                'nom_en' => 'Wedding/Sports Association',
                'nom_ar' => 'زفاف/جمعية رياضية',
                'code' => 'MARIAGE_SPORT',
                'documents' => null,
                'status' => true
            ],
            [
                'nom_fr' => 'Autres',
                'nom_en' => 'Others',
                'nom_ar' => 'أخرى',
                'code' => 'AUTRES',
                'documents' => null,
                'status' => true
            ]
        ];

        foreach ($locationTypes as $locationType) {
            LocationType::create($locationType);
        }
    }
}
