<?php

namespace Database\Seeders;

use App\Models\Config;
use Illuminate\Database\Seeder;

class ConfigSeeder extends Seeder
{
    /**
     * .Run the database seeds.
     */
    public function run(): void
    {
        $configs = [
            // Configurations générales
            /*[
                'key' => 'app_name',
                'value' => 'My App',
                'type' => 'string',
                'group' => 'general',
                'label_fr' => 'Nom de l\'application',
                'label_en' => 'Application Name',
                'label_ar' => 'اسم التطبيق',
                'description_fr' => 'Le nom de l\'application affiché dans l\'interface',
                'description_en' => 'The application name displayed in the interface',
                'description_ar' => 'اسم التطبيق المعروض في الواجهة',
                'is_public' => true,
                'is_system' => true
            ],
            [
                'key' => 'app_description',
                'value' => 'My App Description',
                'type' => 'string',
                'group' => 'general',
                'label_fr' => 'Description de l\'application',
                'label_en' => 'Application Description',
                'label_ar' => 'وصف التطبيق',
                'description_fr' => 'La description de l\'application',
                'description_en' => 'The application description',
                'description_ar' => 'وصف التطبيق',
                'is_public' => true,
                'is_system' => true
            ],
            
            // Configurations d'email
            [
                'key' => 'mail_from_address',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'mail',
                'label_fr' => 'Adresse email d\'expédition',
                'label_en' => 'From Email Address',
                'label_ar' => 'عنوان البريد الإلكتروني للمرسل',
                'description_fr' => 'L\'adresse email utilisée pour envoyer des emails',
                'description_en' => 'The email address used to send emails',
                'description_ar' => 'عنوان البريد الإلكتروني المستخدم لإرسال رسائل البريد الإلكتروني',
                'is_public' => false,
                'is_system' => true
            ],
            [
                'key' => 'mail_from_name',
                'value' => 'My App',
                'type' => 'string',
                'group' => 'mail',
                'label_fr' => 'Nom d\'expédition',
                'label_en' => 'From Name',
                'label_ar' => 'اسم المرسل',
                'description_fr' => 'Le nom utilisé pour envoyer des emails',
                'description_en' => 'The name used to send emails',
                'description_ar' => 'الاسم المستخدم لإرسال رسائل البريد الإلكتروني',
                'is_public' => false,
                'is_system' => true
            ],
            
            // Configurations de l'interface
            [
                'key' => 'primary_color',
                'value' => '#3490dc',
                'type' => 'string',
                'group' => 'ui',
                'label_fr' => 'Couleur primaire',
                'label_en' => 'Primary Color',
                'label_ar' => 'اللون الأساسي',
                'description_fr' => 'La couleur primaire utilisée dans l\'interface',
                'description_en' => 'The primary color used in the interface',
                'description_ar' => 'اللون الأساسي المستخدم في الواجهة',
                'is_public' => true,
                'is_system' => false
            ],
            [
                'key' => 'secondary_color',
                'value' => '#38c172',
                'type' => 'string',
                'group' => 'ui',
                'label_fr' => 'Couleur secondaire',
                'label_en' => 'Secondary Color',
                'label_ar' => 'اللون الثانوي',
                'description_fr' => 'La couleur secondaire utilisée dans l\'interface',
                'description_en' => 'The secondary color used in the interface',
                'description_ar' => 'اللون الثانوي المستخدم في الواجهة',
                'is_public' => true,
                'is_system' => false
            ],
            
            // Configurations des fonctionnalités
            [
                'key' => 'enable_registration',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'features',
                'label_fr' => 'Activer l\'inscription',
                'label_en' => 'Enable Registration',
                'label_ar' => 'تمكين التسجيل',
                'description_fr' => 'Activer ou désactiver l\'inscription des utilisateurs',
                'description_en' => 'Enable or disable user registration',
                'description_ar' => 'تمكين أو تعطيل تسجيل المستخدمين',
                'is_public' => true,
                'is_system' => true
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'features',
                'label_fr' => 'Mode maintenance',
                'label_en' => 'Maintenance Mode',
                'label_ar' => 'وضع الصيانة',
                'description_fr' => 'Activer ou désactiver le mode maintenance',
                'description_en' => 'Enable or disable maintenance mode',
                'description_ar' => 'تمكين أو تعطيل وضع الصيانة',
                'is_public' => true,
                'is_system' => true
            ],
            
            // Configurations des limites
            [
                'key' => 'max_upload_size',
                'value' => '10',
                'type' => 'integer',
                'group' => 'limits',
                'label_fr' => 'Taille maximale de téléchargement (MB)',
                'label_en' => 'Maximum Upload Size (MB)',
                'label_ar' => 'الحد الأقصى لحجم التحميل (ميغابايت)',
                'description_fr' => 'La taille maximale de téléchargement en mégaoctets',
                'description_en' => 'The maximum upload size in megabytes',
                'description_ar' => 'الحد الأقصى لحجم التحميل بالميغابايت',
                'is_public' => true,
                'is_system' => true
            ],
            [
                'key' => 'pagination_limit',
                'value' => '15',
                'type' => 'integer',
                'group' => 'limits',
                'label_fr' => 'Limite de pagination',
                'label_en' => 'Pagination Limit',
                'label_ar' => 'حد الصفحات',
                'description_fr' => 'Le nombre d\'éléments par page',
                'description_en' => 'The number of items per page',
                'description_ar' => 'عدد العناصر في الصفحة',
                'is_public' => true,
                'is_system' => false
            ],
            
            // Configurations des médias sociaux
            [
                'key' => 'social_links',
                'value' => json_encode([
                    'facebook' => 'https://facebook.com/myapp',
                    'twitter' => 'https://twitter.com/myapp',
                    'instagram' => 'https://instagram.com/myapp'
                ]),
                'type' => 'json',
                'group' => 'social',
                'label_fr' => 'Liens sociaux',
                'label_en' => 'Social Links',
                'label_ar' => 'روابط التواصل الاجتماعي',
                'description_fr' => 'Les liens vers les médias sociaux',
                'description_en' => 'Links to social media',
                'description_ar' => 'روابط وسائل التواصل الاجتماعي',
                'is_public' => true,
                'is_system' => false
            ]*/
        ];

        foreach ($configs as $config) {
            Config::updateOrCreate(['key' => $config['key']], $config);
        }
    }
}
