<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Campaign;
use Illuminate\Auth\Access\HandlesAuthorization;

class CampaignPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('view_campaigns');
    }

    public function view(Admin $admin, Campaign $campaign): bool
    {
        return $admin->can('view_campaigns');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_campaigns');
    }

    public function update(Admin $admin, Campaign $campaign): bool
    {
        return $admin->can('edit_campaigns');
    }

    public function delete(Admin $admin, Campaign $campaign): bool
    {
        return $admin->can('delete_campaigns');
    }
}