<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('option_seasons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('option_id')
                ->constrained('options')
                ->onDelete('cascade');
            $table->foreignId('season_id')
                ->constrained('seasons')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('option_seasons');
    }
};

