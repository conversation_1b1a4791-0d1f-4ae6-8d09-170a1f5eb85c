<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class Transaction extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    protected $primaryKey = 'payment_id';

    protected $fillable = [
        'subscription_id',
        'client_id',
        'amount',
        'payment_date',
        'payment_mode',
        'payment_method_id',
        'status',
        'transaction_reference',
        'online_gateway',
        'sale_point_id',
        'employee_id',
        'online_user_agent',
        'notes',
        'payment_details',
        'id_sale_period'
    ];

    protected $casts = [
        'payment_date' => 'datetime',
        'amount' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'payment_details' => 'array'
    ];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    public function salePoint(): BelongsTo
    {
        return $this->belongsTo(SalePoint::class, 'sale_point_id');
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'employee_id');
    }

    public function salePeriod(): BelongsTo
    {
        return $this->belongsTo(SalePeriod::class, 'id_sale_period');
    }
}

