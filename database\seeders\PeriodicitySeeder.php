<?php

namespace Database\Seeders;

use App\Models\Periodicity;
use Illuminate\Database\Seeder;

class PeriodicitySeeder extends Seeder
{
    public function run(): void
    {
        $periodicities = [
            [
                'nom_fr' => 'Mensuelle',
                'nom_en' => 'Monthly',
                'nom_ar' => 'إشتراك شهري',
                'periodicity_code' => 'MONTHLY',
                'max_days_per_week' => 2
            ],
            [
                'nom_fr' => 'Semestrielle', 
                'nom_en' => 'SEMESTRIEL',     
                'nom_ar' => 'إشتراك سداسي',
                'periodicity_code' => 'SEMESTRIEL',
                'max_days_per_week' => 2
            ],
            [
                'nom_fr' => 'Trimestrielle',
                'nom_en' => 'Quarterly',   
                'nom_ar' => 'إشتراك ثلاثي',
                'periodicity_code' => 'TRIMESTRIEL',
                'max_days_per_week' => 2
            ],
            [
                'nom_fr' => 'Annuelle',
                'nom_en' => 'Yearly',
                'nom_ar' => 'إشتراك سنوي',
                'periodicity_code' => 'YEARLY',
                'max_days_per_week' => 2
            ],
        ];


        foreach ($periodicities as $periodicity) {
            Periodicity::create($periodicity);
        }
    }
}
