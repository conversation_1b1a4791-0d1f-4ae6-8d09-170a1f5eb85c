<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

class InsertCategoriesAndPermissions extends Migration
{
    public function up()
    {

        // Insertion des permissions dans la table permissions
        $categories = [
            'roles_permissions',
            'admins',
            'governorates',
            'delegations',
            'establishment_types',
            'school_degrees',
            'establishments',
            'seasons',
            'stations',
            'routes',
            'lines',
            'type_vehicules',
            'location_types',
            'location_seasons',
            'vehicle_season_pricing',
            'type_vehicle_type_locations',
            'academic_years',
            'campaigns',
            'sales_periods',
            'periodicities',
            'agencies',
            'sales_points',
            'assign_agents',
            'stock_cards',
            'card_types',
            'abn_types',
            'payment_methods',
            'duplicate_motifs',
            'tariff_bases',
            'cards_fees',
            'discounts',
            //'client_types',
            //'clients',
            'newSubs',
            'custom_exceptions',
            "options"
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => 'manage_' . $category,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        foreach ($categories as $permission) {
            $category = DB::table('categories')->where('name', 'manage_' . $permission)->first();
            
            if($permission == 'custom_exceptions') continue;
            
            if ($category) {
                DB::table('permissions')->insert([
                    'name' => 'manage_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'view_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'create_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'edit_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'delete_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        $exceptionCategory = DB::table('categories')->where('name', 'manage_custom_exceptions')->first();
        if ($exceptionCategory) {
            $customExceptions = [
                'dashboard',
                'stats',
                'audis_stats',
                'audits',
                //'abn_trajet',
                //'abn_establishments',
                //'abn_photos_firstname_lastname',
                'reimpression',
                'editing_payed_subscriptions',
                'abn_convention_impersonnels',
                'abn_impersonnels',
                'duplicata',
                'custom_discount',
                'remise_except',
                'cancel_transactions',
                'export_excel_subs'
            ];
            
            foreach ($customExceptions as $exception) {
                DB::table('permissions')->insert([
                    'name' => 'manage_' . $exception,
                    'category_id' => $exceptionCategory->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    public function down()
    {
        DB::table('categories')->delete();
        DB::table('permissions')->delete();
    }


}

