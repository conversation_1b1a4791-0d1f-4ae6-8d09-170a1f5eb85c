<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTransactionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'transaction' => 'required|array',
            'transaction.subscription_id' => 'required|exists:subscriptions,id',
            'transaction.client_id' => 'required|exists:clients,id',
            'transaction.governorate_id' => 'sometimes|required|exists:governorates,id',
            'transaction.amount' => 'required|numeric|min:0',
            'transaction.payment_date' => 'required|date',
            'transaction.payment_mode' => 'required|string|in:en_ligne,guichet,social_affair',
            'transaction.payment_method_id' => 'nullable',
            'transaction.status' => 'required|string|in:completed,pending,failed,refunded',
            'transaction.transaction_reference' => 'required|string',
            'transaction.notes' => 'nullable|string',
            'transaction.payment_details' => 'nullable|array',
            'subscription' => 'required|array',
            'subscription.id' => 'required|exists:subscriptions,id',
            'subscription.status' => 'required|string|in:PAYED'
        ];
    }
}