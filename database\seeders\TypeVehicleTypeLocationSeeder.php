<?php

namespace Database\Seeders;

use App\Models\LocationType;
use App\Models\TypeVehicle;
use App\Models\TypeVehicleTypeLocation;
use App\Models\TypeVehicule;
use Illuminate\Database\Seeder;

class TypeVehicleTypeLocationSeeder extends Seeder
{
    public function run(): void
    {
        // Get all type vehicules
        $typeVehicules = TypeVehicule::all();
        
        // Get all location types
        $locationTypes = LocationType::all();
        
        // Create relationships with random km_min values
        foreach ($typeVehicules as $typeVehicule) {
            foreach ($locationTypes as $locationType) {
                TypeVehicleTypeLocation::create([
                    'id_type_vehicule' => $typeVehicule->id,
                    'id_type_location' => $locationType->id,
                    'km_min' => rand(200, 300),
                    'status' => true
                ]);
            }
        }
    }
}
