<?php

namespace Database\Seeders;

use App\Models\LocationSeason;
use Illuminate\Database\Seeder;

class LocationSeasonSeeder extends Seeder
{
    public function run(): void
    {
        LocationSeason::create([
            'nom_fr' => '2024-2025 - Location',
            'nom_en' => '2024-2025 - Location',
            'nom_ar' => '2024-2025 - إيجار',
            'start_date' => '2024-06-21',
            'end_date' => '2024-09-22',
            'status' => true
        ]);

        LocationSeason::create([
            'nom_fr' => '2023-2024 - Location',
            'nom_en' => '2023-2024 - Location',
            'nom_ar' => '2023-2024 - إيجار',
            'start_date' => '2024-09-23',
            'end_date' => '2024-12-20',
            'status' => true
        ]);
    }
}
