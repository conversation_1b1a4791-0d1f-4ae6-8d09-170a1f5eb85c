<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateApi<PERSON>ey extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:generate-key {--show : Display the key instead of modifying files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a secure API key for external applications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $key = 'srtgn_' . Str::random(40);

        if ($this->option('show')) {
            $this->line('<comment>Generated API Key:</comment>');
            $this->line($key);
            $this->newLine();
            $this->line('<info>Add this to your .env file:</info>');
            $this->line("EXTERNAL_API_KEY={$key}");
            return;
        }

        // Update .env file
        $envPath = base_path('.env');
        
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            
            if (strpos($envContent, 'EXTERNAL_API_KEY=') !== false) {
                // Replace existing key
                $envContent = preg_replace(
                    '/EXTERNAL_API_KEY=.*/',
                    "EXTERNAL_API_KEY={$key}",
                    $envContent
                );
            } else {
                // Add new key
                $envContent .= "\n# External API Security\nEXTERNAL_API_KEY={$key}\n";
            }
            
            file_put_contents($envPath, $envContent);
            $this->info('API key generated and added to .env file successfully!');
        } else {
            $this->error('.env file not found. Please create it first.');
            $this->line('<info>Generated key:</info> ' . $key);
        }

        $this->newLine();
        $this->line('<comment>Your API endpoint:</comment>');
        $this->line(config('app.url') . '/api/external/transactions/previous-day');
        $this->newLine();
        $this->line('<comment>Usage example:</comment>');
        $this->line('curl -H "X-API-Key: ' . $key . '" ' . config('app.url') . '/api/external/transactions/previous-day');
    }
}
