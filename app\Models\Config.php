<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Config extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label_fr',
        'label_en',
        'label_ar',
        'description_fr',
        'description_en',
        'description_ar',
        'is_public',
        'is_system'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_system' => 'boolean'
    ];

    /**
     * Get the value with the appropriate type.
     *
     * @return mixed
     */
    public function getTypedValueAttribute()
    {
        switch ($this->type) {
            case 'integer':
                return (int) $this->value;
            case 'float':
                return (float) $this->value;
            case 'boolean':
                return filter_var($this->value, FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($this->value, true);
            case 'array':
                return explode(',', $this->value);
            default:
                return $this->value;
        }
    }

    /**
     * Set the value with the appropriate type.
     *
     * @param mixed $value
     * @return void
     */
    public function setTypedValueAttribute($value)
    {
        switch ($this->type) {
            case 'json':
                $this->attributes['value'] = json_encode($value);
                break;
            case 'array':
                $this->attributes['value'] = is_array($value) ? implode(',', $value) : $value;
                break;
            case 'boolean':
                $this->attributes['value'] = $value ? '1' : '0';
                break;
            default:
                $this->attributes['value'] = (string) $value;
                break;
        }
    }

    /**
     * Get a configuration value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValueByKey(string $key, $default = null)
    {
        $config = self::where('key', $key)->first();

        if (!$config) {
            return $default;
        }

        return $config->typed_value;
    }

    /**
     * Set a configuration value by key.
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @return Config
     */
    public static function setValueByKey(string $key, $value, string $type = 'string')
    {
        $config = self::firstOrNew(['key' => $key]);
        $config->type = $type;
        $config->typed_value = $value;
        $config->save();

        return $config;
    }
}
