<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TariffResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_tariff_base' => $this->id_tariff_base,
            'tariff' => $this->tariff,
            'date_subscription' => $this->date_subscription,
            'date_website' => $this->date_website,
            'tariff_base' => new TariffBaseResource($this->whenLoaded('tariffBase')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
