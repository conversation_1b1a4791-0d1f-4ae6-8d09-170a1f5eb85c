<?php

return [
    // Model names for audit system
    'admin' => 'Administrator',
    'client' => 'Client',
    'subscription' => 'Subscription',
    'transaction' => 'Transaction',
    'agency' => 'Agency',
    'delegation' => 'Delegation',
    'governorate' => 'Governorate',
    'establishment' => 'Establishment',
    'type_establishment' => 'Establishment Type',
    'degree' => 'Degree',
    'payment_method' => 'Payment Method',
    'card_type' => 'Card Type',
    'subs_type' => 'Subscription Type',
    'tariff_base' => 'Tariff Base',
    'line' => 'Line',
    'station' => 'Station',
    'season' => 'Season',
    'trip' => 'Trip',
    'tariff_option' => 'Tariff Option',
    'campaign' => 'Campaign',
    'sale_period' => 'Sale Period',
    'sale_point' => 'Sale Point',
    'affectation_agent' => 'Agent Assignment',
    'type_client' => 'Client Type',
    'periodicity' => 'Periodicity',
    'discount' => 'Discount',
    'card_fee' => 'Card Fee',
    'location_type' => 'Location Type',
    'location_season' => 'Location Season',
    'type_vehicule' => 'Vehicle Type',
    'type_vehicle_type_location' => 'Vehicle Type - Location Type',
    'type_vehicule_saison_location' => 'Vehicle Type - Location Season',
    'social_affair' => 'Social Affair',
    'academic_year' => 'Academic Year',
    'subs_duplication' => 'Subscription Duplication',
    'stock_card' => 'Card Stock',
    'subs_card' => 'Subscription Card',
    'card_sequence' => 'Card Sequence',
    'motif_duplicate' => 'Duplication Reason',
    'governorate_purchase_order' => 'Governorate Purchase Order',
    'affectation_card_type' => 'Card Type Assignment',
    'config' => 'Configuration',
    'audit' => 'Audit',

    // Event types
    'events' => [
        'created' => 'Created',
        'updated' => 'Updated',
        'deleted' => 'Deleted',
        'restored' => 'Restored'
    ],

    // Field names commonly used in audits
    'fields' => [
        'id' => 'ID',
        'nom_fr' => 'Name (FR)',
        'nom_en' => 'Name (EN)',
        'nom_ar' => 'Name (AR)',
        'firstname' => 'First Name',
        'lastname' => 'Last Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'address' => 'Address',
        'status' => 'Status',
        'amount' => 'Amount',
        'date' => 'Date',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'code' => 'Code',
        'ref' => 'Reference',
        'is_active' => 'Active',
        'is_moral' => 'Legal Entity',
        'society_name' => 'Company Name',
        'identity_number' => 'Identity Number',
        'dob' => 'Date of Birth'
    ]
];
