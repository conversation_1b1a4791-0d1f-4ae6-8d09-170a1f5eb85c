<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sale_points', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('contact');
            $table->string('address');
            $table->boolean('status')->default(true);
            $table->foreignId('id_delegation')->constrained('delegations');
            $table->foreignId('id_governorate')->constrained('governorates');
            $table->foreignId('id_agency')->constrained('agencies');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sale_points');
    }
};
