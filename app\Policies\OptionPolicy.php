<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Option;

class OptionPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_options');
    }

    public function view(Admin $admin, Option $option): bool
    {
        return $admin->hasPermissionTo('view_options');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_options');
    }

    public function update(Admin $admin, Option $option): bool
    {
        return $admin->hasPermissionTo('edit_options');
    }

    public function delete(Admin $admin, Option $option): bool
    {
        return $admin->hasPermissionTo('delete_options');
    }
}
