<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('affectation_agent_payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_affectation_agent')->constrained('affectation_agents')->onDelete('cascade');
            $table->foreignId('id_payment_method')->constrained('payment_methods')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['id_affectation_agent', 'id_payment_method'], 'agent_payment_method_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('affectation_agent_payment_methods');
    }
};
