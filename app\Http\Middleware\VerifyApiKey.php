<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpFoundation\Response;

class VerifyApiKey
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = $request->header('X-API-Key') ?? $request->query('api_key');

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API Key is required',
                'error' => 'Missing X-API-Key header or api_key parameter'
            ], 401);
        }

        // Get the expected API key from environment
        $expectedApiKey = config('app.external_api_key');

        if (!$expectedApiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API Key configuration error',
                'error' => 'External API key not configured'
            ], 500);
        }

        // Verify the API key
        if (!hash_equals($expectedApiKey, $apiKey)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API Key',
                'error' => 'The provided API key is not valid'
            ], 401);
        }

        // Log the API access for security monitoring
        \Log::info('External API access', [
            'endpoint' => $request->path(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()
        ]);

        return $next($request);
    }
}
