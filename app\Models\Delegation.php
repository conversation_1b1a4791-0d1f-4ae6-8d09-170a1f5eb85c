<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Delegation extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_governorate'
    ];

    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'id_governorate');
    }

    public function establishments(): HasMany
    {
        return $this->hasMany(Establishment::class, 'id_delegation');
    }

    public function agencies(): HasMany
    {
        return $this->hasMany(Agency::class, 'id_delegation');
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class, 'id_delegation');
    }
}
