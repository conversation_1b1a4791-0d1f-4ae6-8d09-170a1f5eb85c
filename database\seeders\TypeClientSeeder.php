<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TypeClientSeeder extends Seeder
{
    public function run(): void
    {
        $typeClients = [
            [
                'nom_fr' => 'Client Scolaire',
                'nom_en' => 'School Client',
                'nom_ar' => 'عميل مدرسي',
                'color' => '#FF5733',
                'is_student' => true,
                'hasCIN' => false,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Client Universitaire',
                'nom_en' => 'University Client',
                'nom_ar' => 'عميل جامعي',
                'color' => '#33FF57',
                'is_student' => true,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Client Civil',
                'nom_en' => 'Civil Client',
                'nom_ar' => 'عميل مدني',
                'color' => '#3357FF',
                'is_student' => false,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Client Impersonnel',
                'nom_en' => 'Impersonal Client',
                'nom_ar' => 'عميل غير شخصي',
                'color' => '#C70039',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => true,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Client conventionné',
                'nom_en' => 'Approved Client',
                'nom_ar' => 'عميل معتمد',
                'color' => '#C70039',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => true,
                'is_conventional' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('type_clients')->insert($typeClients);
    }
}


