<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuditResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'event' => $this->event,
            'event_name' => $this->getTranslatedEventName(),
            'auditable_type' => $this->auditable_type,
            'auditable_id' => $this->auditable_id,
            'model_name' => $this->getTranslatedModelName(),
            'model_translation_key' => $this->getModelTranslationKey(),
            'user_id' => $this->user_id,
            'user_type' => $this->user_type,
            'user_name' => $this->user_name,
            'user' => $this->whenLoaded('user', function () {
                if ($this->user instanceof \App\Models\Admin) {
                    return [
                        'id' => $this->user->id,
                        'type' => 'admin',
                        'name' => $this->user->firstname . ' ' . $this->user->lastname,
                        'email' => $this->user->email,
                        'roles' => $this->user->roles->pluck('name')->toArray()
                    ];
                } elseif ($this->user instanceof \App\Models\Client) {
                    return [
                        'id' => $this->user->id,
                        'type' => 'client',
                        'name' => $this->user->is_moral
                            ? $this->user->society_name
                            : $this->user->firstname . ' ' . $this->user->lastname,
                        'email' => $this->user->email,
                        'is_moral' => $this->user->is_moral
                    ];
                }
                return null;
            }),
            'auditable' => $this->whenLoaded('auditable', function () {
                if ($this->auditable) {
                    $auditable = [
                        'id' => $this->auditable->id,
                        'type' => class_basename($this->auditable_type)
                    ];

                    switch (class_basename($this->auditable_type)) {
                        case 'Admin':
                            $auditable['name'] = $this->auditable->firstname . ' ' . $this->auditable->lastname;
                            $auditable['email'] = $this->auditable->email;
                            break;
                        case 'Client':
                            $auditable['name'] = $this->auditable->is_moral
                                ? $this->auditable->society_name
                                : $this->auditable->firstname . ' ' . $this->auditable->lastname;
                            break;
                        case 'Subscription':
                            $auditable['ref'] = $this->auditable->ref ?? null;
                            $auditable['status'] = $this->auditable->status ?? null;
                            break;
                        case 'Transaction':
                            $auditable['amount'] = $this->auditable->amount ?? null;
                            $auditable['status'] = $this->auditable->status ?? null;
                            $auditable['reference'] = $this->auditable->transaction_reference ?? null;
                            break;
                        default:
                            if (isset($this->auditable->nom_fr)) {
                                $auditable['name'] = $this->auditable->nom_fr;
                            } elseif (isset($this->auditable->name)) {
                                $auditable['name'] = $this->auditable->name;
                            }
                            break;
                    }

                    return $auditable;
                }
                return null;
            }),
            'old_values' => $this->old_values,
            'new_values' => $this->new_values,
            'changes' => $this->changes,
            'url' => $this->url,
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
            'tags' => $this->tags,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'formatted_date' => $this->created_at?->format('Y-m-d H:i:s'),
            'human_date' => $this->created_at?->diffForHumans(),
        ];
    }

    /**
     * Get translated model name.
     */
    private function getTranslatedModelName(): string
    {
        $translationKey = $this->getModelTranslationKey();
        return __('models.' . $translationKey, [], app()->getLocale()) ?: $this->model_name;
    }

    /**
     * Get model translation key.
     */
    private function getModelTranslationKey(): string
    {
        if (!$this->auditable_type) {
            return 'unknown';
        }

        $className = class_basename($this->auditable_type);
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $className));
    }

    /**
     * Get translated event name.
     */
    private function getTranslatedEventName(): string
    {
        return __('models.events.' . $this->event, [], app()->getLocale()) ?: ucfirst($this->event);
    }
}
