<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSocialAffairRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'governorate_id' => 'exists:governorates,id',
            'academic_year_id' => 'nullable|exists:academic_years,id',
            'delegation' => 'string|max:255',
            'eleve_etudiant' => 'string|in:eleve,etudiant',
            'societe' => 'string|max:255',
            'nom_parent' => 'string|max:255',
            'cin_parent' => 'string|max:255',
            'identifier' => 'string|max:255',
            'dob' => 'date',
            'telephone' => 'string|max:255',
            'nom_complet' => 'string|max:255',
            'niveau_etude' => 'string|max:255',
            'trajet_requise' => 'string|max:255'
        ];
    }
}
