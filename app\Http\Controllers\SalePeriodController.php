<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSalePeriodRequest;
use App\Http\Requests\UpdateSalePeriodRequest;
use App\Http\Resources\SalePeriodResource;
use App\Models\Campaign;
use App\Models\SalePeriod;
use App\Repositories\SalePeriodRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;

class SalePeriodController extends Controller
{
    private SalePeriodRepository $repository;

    public function __construct(SalePeriodRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SalePeriod::class, 'sale_period');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return SalePeriodResource::collection(
            $this->repository->with(['campaign.abnType', 'affectationAgents', 'periodicity'])->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return SalePeriodResource::collection(
            $this->repository->with(['campaign', 'affectationAgents', 'periodicity'])->all()
        );
    }

    public function store(StoreSalePeriodRequest $request): JsonResponse
    {
        $data = $request->validated();

        $campaign = Campaign::find($data['id_campaign']);
        if ($campaign) {
            $existingActivePeriod = SalePeriod::whereHas('campaign', function($query) use ($campaign) {
                    $query->where('id_abn_type', $campaign->id_abn_type);
                })
                ->orWhere(function($query) {
                    $query->where('date_start_client', '<=', now())
                          ->where('date_end_client', '>=', now());
                })
                ->orWhere(function($query) {
                    $query->where('date_start_agent', '<=', now())
                          ->where('date_end_agent', '>=', now());
                })
                ->orWhere(function($query) {
                    $query->where('date_start_validity', '<=', now())
                          ->where('date_end_validity', '>=', now());
                })
                ->first();

            if ($existingActivePeriod) {
                return response()->json([
                    'message' => __('sale_period.already_active_for_subs_type'),
                ], 422);
            }
        }

        $salePeriod = $this->repository->create($data);

        return response()->json([
            'message' => __('sale_period.created_successfully'),
            'data' => new SalePeriodResource($salePeriod->load(['campaign.abnType', 'affectationAgents']))
        ], 201);
    }

    public function show(SalePeriod $salePeriod): SalePeriodResource
    {
        return new SalePeriodResource($salePeriod->load(['campaign.abnType', 'affectationAgents']));
    }

    public function update(UpdateSalePeriodRequest $request, $id): JsonResponse
    {
        try {
            $salePeriod = $this->repository->find($id);
            if (!$salePeriod) {
                return response()->json([
                    'message' => __('messages.sale_period.not_found'),
                ], Response::HTTP_NOT_FOUND);
            }

            $data = $request->validated();

            $campaign = Campaign::find($data['id_campaign'] ?? $salePeriod->id_campaign);
            if ($campaign) {
                $existingActivePeriod = SalePeriod::whereHas('campaign', function($query) use ($campaign) {
                        $query->where('id_abn_type', $campaign->id_abn_type);
                    })
                    ->where('id', '!=', $id)
                    ->where(function($query) {
                        $query->where(function($subQuery) {
                            $subQuery->where('date_start_client', '<=', now())
                                     ->where('date_end_client', '>=', now());
                        })
                        ->orWhere(function($subQuery) {
                            $subQuery->where('date_start_agent', '<=', now())
                                     ->where('date_end_agent', '>=', now());
                        })
                        ->orWhere(function($subQuery) {
                            $subQuery->where('date_start_validity', '<=', now())
                                     ->where('date_end_validity', '>=', now());
                        });
                    })
                    ->first();

                if ($existingActivePeriod) {
                    return response()->json([
                        'message' => __('sale_period.already_active_for_subs_type'),
                    ], 422);
                }
            }

            $updated = $this->repository->update($data, $id);
            return response()->json([
                'message' => __('sale_period.updated_successfully'),
                'data' => new SalePeriodResource($updated->load(['campaign.abnType', 'affectationAgents']))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating sale period',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            $salePeriod = $this->repository->find($id);
            if (!$salePeriod) {
                return response()->json([
                    'message' => __('sale_period.not_found'),
                ], Response::HTTP_NOT_FOUND);
            }
            $this->repository->delete($id);
            return response()->json([
                'message' => __('sale_period.deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting sale period',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getByCampaign($campaignId): AnonymousResourceCollection
    {
        $salePeriods = $this->repository
            ->with(['campaign.abnType', 'affectationAgents'])
            ->findWhere(['id_campaign' => $campaignId]);

        return SalePeriodResource::collection($salePeriods);
    }
}







