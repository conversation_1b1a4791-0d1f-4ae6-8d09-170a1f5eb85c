<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Models\Line;
use App\Models\Season;
use Illuminate\Http\JsonResponse;

class WebsiteLinesController extends Controller
{
    public function getLines(): JsonResponse
    {
        $lines = Line::where('status', true)
            ->select('id', 'CODE_LINE', 'nom_fr', 'nom_ar', 'nom_en')
            ->get();

        return response()->json($lines);
    }

    public function getLineDetails(Line $line): JsonResponse
    {
        if (!$line->status) {
            return response()->json(['message' => 'Line not found'], 404);
        }

        // Précharger les saisons
        $seasons = Season::all()->keyBy('id');

        $line->load([
            'lineStations' => function ($query) {
                $query->orderBy('position');
            },
            'lineStations.station.delegation',
            'lineStations.station.governorate',
        ]);

        return response()->json([
            'id' => $line->id,
            'code_line' => $line->CODE_LINE,
            'nom_fr' => $line->nom_fr,
            'nom_ar' => $line->nom_ar,
            'nom_en' => $line->nom_en,
            'type_service' => $line->type_service,
            'status' => $line->status,
            'commercial_speed' => $line->commercial_speed,
            'created_at' => $line->created_at,
            'updated_at' => $line->updated_at,
            'coordinates' => $line->lineStations->map(fn($ls) => [
                'id' => $ls->station->id,
                'nom_fr' => $ls->station->nom_fr,
                'nom_ar' => $ls->station->nom_ar,
                'nom_en' => $ls->station->nom_en,
                'longitude' => $ls->station->longitude,
                'latitude' => $ls->station->latitude,
                'position' => $ls->position,
                'departure_times' => $ls->start_time ? collect($ls->start_time)->map(function($times, $seasonId) use ($seasons) {
                    $season = $seasons->get((int)$seasonId);
                    return [
                        'season_nom_fr' => $season ? $season->nom_fr : null,
                        'season_nom_en' => $season ? $season->nom_en : null,
                        'season_nom_ar' => $season ? $season->nom_ar : null,
                        'times' => $times
                    ];
                })->values()->all() : null,
                'type' => $ls->station->type,
                'delegation' => $ls->station->delegation ? [
                    'id' => $ls->station->delegation->id,
                    'nom_fr' => $ls->station->delegation->nom_fr,
                    'nom_ar' => $ls->station->delegation->nom_ar,
                    'nom_en' => $ls->station->delegation->nom_en,
                ] : null,
                'governorate' => $ls->station->governorate ? [
                    'id' => $ls->station->governorate->id,
                    'nom_fr' => $ls->station->governorate->nom_fr,
                    'nom_ar' => $ls->station->governorate->nom_ar,
                    'nom_en' => $ls->station->governorate->nom_en,
                ] : null,
                'created_at' => $ls->station->created_at,
                'updated_at' => $ls->station->updated_at
            ]),

            'routes' => $line->trips->map(function ($trip) {
                return [
                    'id' => $trip->id,
                    'number_of_km' => $trip->number_of_km,
                    'station_depart' => [
                        'id' => $trip->startStation->id,
                        'nom_fr' => $trip->startStation->nom_fr,
                        'nom_ar' => $trip->startStation->nom_ar,
                        'nom_er' => $trip->startStation->nom_er,
                    ],
                    'station_arrival' => [
                        'id' => $trip->endStation->id,
                        'nom_fr' => $trip->endStation->nom_fr,
                        'nom_ar' => $trip->endStation->nom_ar,
                        'nom_en' => $trip->endStation->nom_en,
                    ]
                ];
            })
        ]);
    }
}





