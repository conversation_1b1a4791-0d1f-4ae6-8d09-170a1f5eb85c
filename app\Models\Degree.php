<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class Degree extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'age_max',
        'id_type_establishment'
    ];

    public function typeEstablishment(): BelongsTo
    {
        return $this->belongsTo(TypeEstablishment::class, 'id_type_establishment');
    }
}


