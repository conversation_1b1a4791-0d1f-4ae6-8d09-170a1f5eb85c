<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->boolean('is_moral')->default(false);
            $table->string('lastname')->nullable();
            $table->string('firstname')->nullable();
            $table->string('society_name')->nullable();
            $table->string('legal_representative')->nullable();
            $table->date('dob')->nullable();
            $table->string('identity_number')->comment('CIN - identifiant eleve - raison sociale/MF - num convention');
            $table->bigInteger('phone');
            $table->string('address');
            $table->boolean('is_withTVA')->nullable();
            $table->foreignId('id_delegation')->constrained('delegations');
            $table->foreignId('id_governorate')->constrained('governorates');
            $table->foreignId('id_establishment')->nullable()->constrained('establishments');
            $table->foreignId('id_degree')->nullable()->constrained('degrees');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
