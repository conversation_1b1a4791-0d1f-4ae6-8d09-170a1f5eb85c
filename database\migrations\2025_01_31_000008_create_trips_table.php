<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('trips', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('id_line')->constrained('lines');
            $table->foreignId('id_station_start')->constrained('stations');
            $table->foreignId('id_station_end')->constrained('stations');
            $table->json('CODE_ABO_SCOL')->nullable();
            $table->json('CODE_ABO_CIVIL')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('inter_station');
            $table->boolean('is_aller_retour')->default(true);
            $table->integer('number_of_km')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('trips');
    }
};
