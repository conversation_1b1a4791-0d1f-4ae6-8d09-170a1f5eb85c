<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;
use Symfony\Component\HttpFoundation\Response;

class VerifyJWT
{
    public function handle(Request $request, Closure $next)
    {
        try {
            if (!$user = JWTAuth::parseToken()->authenticate()) {
                return response()->json(['error' => 'Utilisateur non authentifié'], Response::HTTP_UNAUTHORIZED);
            }
        } catch (JWTException $e) {
            return response()->json(['error' => 'Token invalide ou expiré'], Response::HTTP_UNAUTHORIZED);
        }

        $request->attributes->set('user', $user);

        return $next($request);
    }
}
