<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Governorate;

class GovernoratePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view_governorates');
    }

    public function view(Admin $admin, Governorate $governorate): bool
    {
        return $admin->hasPermissionTo('view_governorates');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_governorates');
    }

    public function update(Admin $admin, Governorate $governorate): bool
    {
        return $admin->hasPermissionTo('edit_governorates');
    }

    public function delete(Admin $admin, Governorate $governorate): bool
    {
        return $admin->hasPermissionTo('delete_governorates');
    }
}