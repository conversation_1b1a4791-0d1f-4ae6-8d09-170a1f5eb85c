<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateAffectationCardTypeRequest;
use App\Models\AffectationCardType;
use App\Models\CardType;
use App\Repositories\AffectationCardTypeRepository;
use App\Http\Requests\StoreAffectationCardTypeRequest;
use App\Http\Resources\AffectationCardTypeResource;
use App\Services\CardSequenceTracker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class AffectationCardTypesController extends Controller
{
    private AffectationCardTypeRepository $repository;
    protected $cardSequenceTracker;

    public function __construct(AffectationCardTypeRepository $repository, CardSequenceTracker $cardSequenceTracker)
    {
        $this->repository = $repository;
        $this->cardSequenceTracker = $cardSequenceTracker;
        $this->authorizeResource(AffectationCardType::class, 'affectation_card_type');
    }
    public function all(): AnonymousResourceCollection
    {
        return AffectationCardTypeResource::collection(
            $this->repository->with(['agent', 'cardType'])->all()
        );
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $affectations = $this->repository->with(['agent', 'cardType'])
            ->latest()
            ->paginate($request->input('perPage'));

        $cardTypes = CardType::all();
        $maxSerialNumbers = [];
        foreach ($cardTypes as $cardType) {
            $maxSerialNumber = AffectationCardType::where('id_card_type', $cardType->id)
                ->max('end_serial_number') ?? 0;

            $maxSerialNumbers[$cardType->id] = [
                'id' => $cardType->id,
                'max_serial_number' => $maxSerialNumber
            ];
        }
        return AffectationCardTypeResource::collection($affectations)
            ->additional([
                'meta' => [
                    'card_type_max_serials' => $maxSerialNumbers,
                ]
            ]);
    }

    public function store(StoreAffectationCardTypeRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            foreach ($request->ranges as $range) {
                $affectationCardType = AffectationCardType::create([
                    'id_agent' => $request->id_agent,
                    'id_card_type' => $range['cardType'],
                    'start_serial_number' => $range['start_serial_number'],
                    'end_serial_number' => $range['end_serial_number'],
                    'current_serial_number' => $range['start_serial_number'],
                ]);
                $this->cardSequenceTracker->assignToAgent(
                    $range['cardType'],
                    $range['start_serial_number'],
                    $range['end_serial_number'],
                    $request->id_agent
                );
            }
            DB::commit();
            return response()->json([
                'message' => 'Affectation created successfully',
                'data' => new AffectationCardTypeResource(
                    $affectationCardType->load(['agent', 'cardType'])
                )
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(AffectationCardType $affectationCardType): AffectationCardTypeResource
    {
        return new AffectationCardTypeResource(
            $affectationCardType->load(['agent', 'cardType'])
        );
    }

    public function update(UpdateAffectationCardTypeRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationCardType = $this->repository->find($id);

            if (!$affectationCardType) {
                return response()->json([
                    'message' => 'Affectation not found',
                    'error' => 'Resource not found'
                ], 404);
            }
            if ($affectationCardType->current_serial_number > $affectationCardType->start_serial_number) {
                return response()->json([
                    'message' => 'Cannot update affectation because cards have already been used',
                    'error' => 'Cards in use'
                ], 422);
            }
            foreach ($request->ranges as $range) {
                $this->cardSequenceTracker->returnToStock(
                    $affectationCardType->id_card_type,
                    $affectationCardType->start_serial_number,
                    $affectationCardType->end_serial_number
                );
                $this->cardSequenceTracker->assignToAgent(
                    $range['cardType'],
                    $range['start_serial_number'],
                    $range['end_serial_number'],
                    $request->id_agent
                );
                $affectationCardType->update([
                    'id_card_type' => $range['cardType'],
                    'start_serial_number' => $range['start_serial_number'],
                    'end_serial_number' => $range['end_serial_number'],
                    'current_serial_number' => $range['start_serial_number'],
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Affectation updated successfully',
                'data' => new AffectationCardTypeResource(
                    $affectationCardType->load(['agent', 'cardType'])
                )
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }    
    
    public function destroy($id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationCardType = $this->repository->find($id);

            if (!$affectationCardType) {
                return response()->json([
                    'message' => 'Affectation not found',
                    'error' => 'Resource not found'
                ], 404);
            }
            if ($affectationCardType->current_serial_number > $affectationCardType->start_serial_number) {
                return response()->json([
                    'message' => 'Cannot delete affectation because cards have already been used',
                    'error' => 'Cards in use'
                ], 422);
            }
            try {
                $this->cardSequenceTracker->returnToStock(
                    $affectationCardType->id_card_type,
                    $affectationCardType->start_serial_number,
                    $affectationCardType->end_serial_number
                );
            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'message' => 'Error returning card sequences to stock',
                    'error' => $e->getMessage()
                ], 500);
            }
            $affectationCardType->delete();
            DB::commit();
            return response()->json([
                'message' => 'Affectation deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error deleting affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}



