<?php

namespace Database\Seeders;

use App\Models\Line;
use App\Models\Station;
use App\Models\LineStation;
use App\Models\TariffBase;
use App\Models\Trip;
use App\Models\TariffOption;
use App\Models\WebsiteTrip;
use Illuminate\Database\Seeder;

class TripScolairSeeder extends Seeder
{
    private $missedStations = [];

    public function run(): void
    {

        include_once("data/SCOLAIRE.php");

        // Get all lines
        $lines = Line::all();

        foreach ($trajet_scolaires as $trajet) {

            $line = Line::where('CODE_LINE', $trajet['CODE_LIGNE'])->first();
            if (!$line) {
                echo "No line found for: {$trajet['CODE_LIGNE']}\n";
                continue;
            }

            $startStation = Station::where('nom_fr', $trajet['CODE_ARRET'])->first();
            if (!$startStation) {
                echo "No start station found for: {$trajet['CODE_ARRET']}\n";
                $this->addMissedStation($trajet['CODE_ARRET']);
                continue;
            }
            $endStation = Station::where('nom_fr', $trajet['ARR_CODE_ARRET'])->first();
            if (!$endStation) {
                echo "No end station found for: {$trajet['ARR_CODE_ARRET']}\n";
                $this->addMissedStation($trajet['ARR_CODE_ARRET']);
                continue;
            }

            $tariffBases = TariffBase::where('id_subs_type', 1)
            ->whereHas('tariffs', function ($query) use ($trajet) {
                return $query->where('tariff', '=',  $trajet['TARIF_BASE_ABO_SCOL'] / 1000);
            })
            ->first();

            $tariffBasesUniv = TariffBase::where('id_subs_type', 2)
            ->whereHas('tariffs', function ($query) use ($trajet) {
                return $query->where('tariff', '=',  $trajet['TARIF_BASE_ABO_SCOL'] / 1000);
            })
            ->first();

            // Check if tariff bases were found
            if (!$tariffBases) {
                $tariffValue = $trajet['TARIF_BASE_ABO_SCOL'] / 1000;
                echo "No tariff base found for subs_type 1 with tariff: {$tariffValue} (original: {$trajet['TARIF_BASE_ABO_SCOL']})\n";
                continue;
            }

            if (!$tariffBasesUniv) {
                $tariffValue = $trajet['TARIF_BASE_ABO_SCOL'] / 1000;
                echo "No tariff base found for subs_type 2 with tariff: {$tariffValue} (original: {$trajet['TARIF_BASE_ABO_SCOL']})\n";
                continue;
            }

            // Check if trip already exists
            $existingTrip = Trip::where('id_station_start', $startStation->id)
                ->where('id_station_end', $endStation->id)
                ->whereHas('lines', function($query) use ($line) {
                    $query->where('lines.id', $line->id);
                })
                ->first();

            if ($existingTrip) {
                // Trip exists, add subscription codes to JSON arrays if not already present
                $currentScolCodes = $existingTrip->CODE_ABO_SCOL ?? [];
                $currentCivilCodes = $existingTrip->CODE_ABO_CIVIL ?? [];

                // Add new SCOL code if not already present
                if ($trajet['CODE_ABO_SCOL'] && !in_array($trajet['CODE_ABO_SCOL'], $currentScolCodes)) {
                    $currentScolCodes[] = $trajet['CODE_ABO_SCOL'];
                }

                // Add new CIVIL code if not already present
                if ($trajet['CODE_ABO_CIVIL'] && $trajet['CODE_ABO_CIVIL'] !== '0' && !in_array($trajet['CODE_ABO_CIVIL'], $currentCivilCodes)) {
                    $currentCivilCodes[] = $trajet['CODE_ABO_CIVIL'];
                }

                // Update the trip with new codes
                $existingTrip->update([
                    'CODE_ABO_SCOL' => $currentScolCodes,
                    'CODE_ABO_CIVIL' => $currentCivilCodes
                ]);

                // Check and add tariff options if not already present
                $existingTariffOption1 = $existingTrip->tariffOptions()
                    ->where('id_subs_type', 1)
                    ->where('is_regular', false)
                    ->where('id_tariff_base', $tariffBases->id)
                    ->first();

                if (!$existingTariffOption1) {
                    $existingTrip->tariffOptions()->create([
                        'id_subs_type' => 1,
                        'is_regular' => false,
                        'id_tariff_base' => $tariffBases->id
                    ]);
                    echo "Added tariff option (subs_type 1) to existing trip: {$existingTrip->nom_fr}\n";
                }

                $existingTariffOption2 = $existingTrip->tariffOptions()
                    ->where('id_subs_type', 2)
                    ->where('is_regular', false)
                    ->where('id_tariff_base', $tariffBasesUniv->id)
                    ->first();

                if (!$existingTariffOption2) {
                    $existingTrip->tariffOptions()->create([
                        'id_subs_type' => 2,
                        'is_regular' => false,
                        'id_tariff_base' => $tariffBasesUniv->id
                    ]);
                    echo "Added tariff option (subs_type 2) to existing trip: {$existingTrip->nom_fr}\n";
                }

                // Update number_of_km if existing is null or 0 and new data has value
                if (($existingTrip->number_of_km === null || $existingTrip->number_of_km == 0) &&
                    $trajet['KILOMERAGE'] && $trajet['KILOMERAGE'] > 0) {
                    $existingTrip->update(['number_of_km' => $trajet['KILOMERAGE']]);
                    echo "Updated number_of_km for existing trip: {$existingTrip->nom_fr}\n";
                }

                echo "Updated subscription codes for existing trip: {$existingTrip->nom_fr}\n";
            } else {
                // Trip doesn't exist, create it with JSON arrays for subscription codes
                $scolCodes = $trajet['CODE_ABO_SCOL'] ? [$trajet['CODE_ABO_SCOL']] : [];
                $civilCodes = ($trajet['CODE_ABO_CIVIL'] && $trajet['CODE_ABO_CIVIL'] !== '0') ? [$trajet['CODE_ABO_CIVIL']] : [];

                $trip = Trip::create([
                    'nom_fr' => $startStation->nom_fr . ' - ' . $endStation->nom_fr,
                    'nom_en' => $startStation->nom_en . ' - ' . $endStation->nom_en,
                    'nom_ar' => $startStation->nom_ar . ' - ' . $endStation->nom_ar,
                    'id_station_start' => $startStation->id,
                    'id_station_end' => $endStation->id,
                    'CODE_ABO_SCOL' => $scolCodes,
                    'CODE_ABO_CIVIL' => $civilCodes,
                    'status' => true,
                    'inter_station' => false,
                    'is_aller_retour' => true,
                    'number_of_km' => $trajet['KILOMERAGE'],
                ]);

                // Attach the line to the trip
                $trip->lines()->attach($line->id);

                $trip->tariffOptions()->create([
                    'id_subs_type' => 1,
                    'is_regular' => false,
                    'id_tariff_base' => $tariffBases->id
                ]);

                $trip->tariffOptions()->create([
                    'id_subs_type' => 2,
                    'is_regular' => false,
                    'id_tariff_base' => $tariffBasesUniv->id
                ]);
                echo "Created new trip: {$trip->nom_fr}\n";
            }
        }

        // Save missed stations to file
        $this->saveMissedStations();
    }

    private function addMissedStation($stationName)
    {
        if (!in_array($stationName, $this->missedStations)) {
            $this->missedStations[] = $stationName;
        }
    }

    private function saveMissedStations()
    {
        if (!empty($this->missedStations)) {
            $filePath = database_path('seeders/missed_stations.txt');

            // Read existing content if file exists
            $existingStations = [];
            if (file_exists($filePath)) {
                $existingContent = file_get_contents($filePath);
                $existingStations = array_filter(explode("\n", $existingContent));
            }

            // Merge with new missed stations (avoiding duplicates)
            $allStations = array_unique(array_merge($existingStations, $this->missedStations));
            sort($allStations);

            // Write back to file
            file_put_contents($filePath, implode("\n", $allStations));

            echo "Saved " . count($this->missedStations) . " missed stations from TripScolairSeeder to missed_stations.txt\n";
        }
    }
}


