<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTypeVehiculeSaisonLocationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_type_vehicule' => [
                'required',
                'exists:type_vehicules,id',
                Rule::unique('type_vehicule_saison_locations', 'id_type_vehicule')
                    ->where('id_saison_location', $this->id_saison_location)
                    ->ignore($this->route('type_vehicule_saison_location'))
            ],
            'id_saison_location' => [
                'required',
                'exists:seasons,id',
                Rule::unique('type_vehicule_saison_locations', 'id_saison_location')
                    ->where('id_type_vehicule', $this->id_type_vehicule)
                    ->ignore($this->route('type_vehicule_saison_location'))
            ],
            'prix_km' => 'nullable|numeric|min:0',
            'status' => 'boolean'
        ];
    }
}

