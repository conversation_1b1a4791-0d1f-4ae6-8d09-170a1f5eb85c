<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\LocationType;

class LocationTypePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_location_types');
    }

    public function view(Admin $admin, LocationType $locationType): bool
    {
        return $admin->hasPermissionTo('view_location_types');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_location_types');
    }

    public function update(Admin $admin, LocationType $locationType): bool
    {
        return $admin->hasPermissionTo('edit_location_types');
    }

    public function delete(Admin $admin, LocationType $locationType): bool
    {
        return $admin->hasPermissionTo('delete_location_types');
    }
}