<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Transaction;

class TransactionPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, Transaction $transaction): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, Transaction $transaction): bool
    {
        return true;
    }

    public function delete(Admin $admin, Transaction $transaction): bool
    {
        return true;
    }
}
