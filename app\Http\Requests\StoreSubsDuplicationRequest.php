<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubsDuplicationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'motif_duplicate_id' => 'required|exists:motif_duplicates,id',
            'subscription_id' => 'required|exists:subscriptions,id',
            
        ];
    }
}

