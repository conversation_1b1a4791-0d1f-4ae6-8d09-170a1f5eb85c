<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Http\Controllers\Api\DemandeTypeController;
use App\Models\AnneeUniversitaire;
use App\Models\Classification;
use App\Models\Country;
use App\Models\Delegation;
use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\Etape;
use App\Models\Governorate;
use App\Models\Admin;
use App\Models\AttestationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class GovernorateDelegationSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     * @throws \Exception
     */
    public function run()
    {
        $govDelArray = [
            [
                "Delegations" => [
                    "Carthage",
                    "La Médina",
                    "Bab Bhar",
                    "Bab Souika",
                    "Omrane",
                    "Omrane Supérieur",
                    "Attahrir",
                    "El Menza<PERSON>",
                    "<PERSON><PERSON> Alkhadhra",
                    "<PERSON><PERSON>",
                    "<PERSON><PERSON><PERSON><PERSON>",
                    "<PERSON><PERSON><PERSON><PERSON>",
                    "<PERSON><PERSON><PERSON><PERSON>",
                    "<PERSON><PERSON>",
                    "<PERSON><PERSON><PERSON>",
                    "<PERSON><PERSON><PERSON>",
                    "<PERSON><PERSON>",
                    "<PERSON><PERSON>",
                    "<PERSON> Goulette",
                    "Le Kram",
                    "La <PERSON>a"
                ],
                "Code" => "11",
                "Nbre" => "21",
                "Gouvernorat" => "Tunis",
                "GouvernoratAr" => "تونس",
                "DelegationsAr" => [
                    "قرطاج",
                    "المدينة",
                    "باب البحر",
                    "باب سويقة",
                    "العمران",
                    "العمران الأعلى",
                    "التحرير",
                    "المنزه",
                    "حي الخضراء",
                    "باردو",
                    "السيجومي",
                    "الزهور",
                    "الحرائرية",
                    "سيدي حسين",
                    "الوردية",
                    "الكبارية",
                    "سيدي البشير",
                    "جبل الجلود",
                    "حلق الوادي",
                    "الكرم",
                    "المرسى"
                ]
            ],
            [
                "Delegations" => [
                    "Ariana Ville",
                    "Soukra",
                    "Raouède",
                    "Kalâat Andalous",
                    "Sidi Thabet",
                    "Cité Attadhamon",
                    "M’nihla"
                ],
                "Code" => "12",
                "Nbre" => "7",
                "Gouvernorat" => "Ariana",
                "GouvernoratAr" => "أريانة",
                "DelegationsAr" => [
                    "أريانة المدينة",
                    "سكرة",
                    "رواد",
                    "قلعة الأندلس",
                    "سيدي ثابت",
                    "حي التضامن",
                    "المنيهلة"
                ]
            ],
            [
                "Delegations" => [
                    "Manouba",
                    "Oued Ellil –Tebourba",
                    "Battan",
                    "Jedaida",
                    "Mornaguia",
                    "Borj Amri",
                    "Douar Hicher"
                ],
                "Code" => "14",
                "Nbre" => "8",
                "Gouvernorat" => "Manouba",
                "GouvernoratAr" => "منوبة",
                "DelegationsAr" => [
                    "منوبة",
                    "وادي الليل",
                    "طبربة",
                    "البطان",
                    "الجديدة",
                    "المرناقية",
                    "برج العامري",
                    "دوار هيشر"
                ]
            ],
            [
                "Delegations" => [
                    "Ben Arous",
                    "Nouvelle Médina",
                    "Mourouj",
                    "Hammam Lif",
                    "Hammam Chatt",
                    "Boumhel Bassatine",
                    "Ezzahra",
                    "Radès",
                    "Megrine",
                    "M’hamdia",
                    "Fouchana",
                    "Mornag"
                ],
                "Code" => "13",
                "Nbre" => "12",
                "Gouvernorat" => "Ben Arous",
                "GouvernoratAr" => "بن عروس",
                "DelegationsAr" => [
                    "بن عروس",
                    "المدينة الجديدة",
                    "المروج",
                    "حمام الأنف",
                    "حمام الشط",
                    "بومهل البساتين",
                    "الزهراء",
                    "رادس",
                    "مقرين",
                    "المحمدية",
                    "فوشانة",
                    "مرناق"
                ]
            ],
            [
                "Delegations" => [
                    "Nabeul",
                    "Dar Chaâbane Elfehri",
                    "Béni Khiar",
                    "Korba",
                    "Menzel Temime",
                    "Mida",
                    "Kelibia",
                    "Hammam Ghezaz",
                    "Haouaria",
                    "Takelsa",
                    "Slimane",
                    "Menzel Bouzelfa",
                    "Béni Khalled",
                    "Grombalia",
                    "Bouârgoub",
                    "Hammamet"
                ],
                "Code" => "15",
                "Nbre" => "16",
                "Gouvernorat" => "Nabeul",
                "GouvernoratAr" => "نابل",
                "DelegationsAr" => [
                    "نابل",
                    "دار شعبان الفهري",
                    "بني خيار",
                    "قربة",
                    "منزل تميم",
                    "الميدة",
                    "قليبية",
                    "حمام الأغزاز",
                    "الهوارية",
                    "تاكلسة",
                    "سليمان",
                    "منزل بوزلفة",
                    "بني خلاد",
                    "قرمبالية",
                    "بوعرقوب",
                    "الحمامات"
                ]
            ],
            [
                "Delegations" => [
                    "Bizerte Nord",
                    "Jarzouna",
                    "Bizerte Sud",
                    "Sejnane",
                    "Joumine",
                    "Mateur",
                    "Ghezala",
                    "Menzel Bourguiba",
                    "Tinja",
                    "Utique",
                    "Ghar El Melh",
                    "Menzel Jemil",
                    "El Alia",
                    "Ras Jebel"
                ],
                "Code" => "17",
                "Nbre" => "14",
                "Gouvernorat" => "Bizerte",
                "GouvernoratAr" => "بنزرت",
                "DelegationsAr" => [
                    "بنزرت الشمالية",
                    "جرزونة",
                    "بنزرت الجنوبية",
                    "سجنان",
                    "جومين",
                    "ماطر",
                    "غزالة",
                    "منزل بورقيبة",
                    "تينجة",
                    "أوتيك",
                    "غار الملح ",
                    "منزل جميل",
                    "العالية ",
                    "رأس الجبل"
                ]
            ],
            [
                "Delegations" => [
                    "Zaghouan",
                    "Zériba",
                    "Bir Mecharga",
                    "Fahs",
                    "Nadhour",
                    "Saouaf"
                ],
                "Code" => "16",
                "Nbre" => "6",
                "Gouvernorat" => "Zaghouan",
                "GouvernoratAr" => "زغوان",
                "DelegationsAr" => [
                    "زغوان",
                    "الزريبة",
                    "بئر مشارقة",
                    "الفحص",
                    "الناظور",
                    "صواف"
                ]
            ],
            [
                "Delegations" => [
                    "Sousse Ville",
                    "Zaouia",
                    "Ksiba",
                    "Thrayat",
                    "Sousse Ryadh",
                    "Sousse Jawhara",
                    "Sousse Sidi Abdelhamid",
                    "Hammam sousse",
                    "Akouda",
                    "Kalâa Elkébira",
                    "Sidi Bouali",
                    "Hergla",
                    "Enfidha",
                    "Bouficha",
                    "Koundar",
                    "Sidi Elheni",
                    "Msaken",
                    "Kalâa Ességhira"
                ],
                "Code" => "31",
                "Nbre" => "16",
                "Gouvernorat" => "Sousse",
                "GouvernoratAr" => "سوسة",
                "DelegationsAr" => [
                    "سوسة المدينة",
                    "الزاوية",
                    "القصيبة",
                    "الثريات ",
                    "سوسة الرياض",
                    "سوسة جوهرة",
                    "سوسة سيدي عبد الحميد",
                    "حمام سوسة",
                    "أكودة",
                    "القلعة الكبرى",
                    "سيدي بوعلي",
                    "هرقلة",
                    "النفيضة",
                    "بوفيشة",
                    "كندار",
                    "سيدي الهاني",
                    "مساكن",
                    "القلعة الصغرى"
                ]
            ],
            [
                "Delegations" => [
                    "Monastir",
                    "Ouerdanine",
                    "Sahline",
                    "Zéramdine",
                    "Béni Hassan",
                    "Jammel",
                    "Benbla",
                    "Moknine",
                    "Bekalta",
                    "Teboulba",
                    "Ksar Helal",
                    "Ksibet Medyouni",
                    "Sayada Lamta Bouhjar"
                ],
                "Code" => "32",
                "Nbre" => "13",
                "Gouvernorat" => "Monastir",
                "GouvernoratAr" => "المنستير",
                "DelegationsAr" => [
                    "المنستيـر",
                    "الوردانيـن",
                    "الساحليـن",
                    "زرمديـن",
                    "بنـي حسان",
                    "جمـال",
                    "بنبلة",
                    "المكنين",
                    "البقالطة",
                    "طبلبة",
                    "قصر هلال",
                    "قصيبة المديوني",
                    "صيادة لمطة بوحجر"
                ]
            ],
            [
                "Delegations" => [
                    "Mahdia",
                    "Boumerdes",
                    "Ouled Chamekh",
                    "Chorbane",
                    "Hbira",
                    "Souassi",
                    "Eljem",
                    "Chebba",
                    "Malloulech",
                    "Sidi Alouane",
                    "Ksour Essef"
                ],
                "Code" => "33",
                "Nbre" => "11",
                "Gouvernorat" => "Mahdia",
                "GouvernoratAr" => "المهدية",
                "DelegationsAr" => [
                    "المهدية",
                    "بومرداس",
                    "أولاد الشامخ",
                    "شربان",
                    "هبيرة",
                    "السواسي",
                    "الجم",
                    "الشابة",
                    "ملولش",
                    "سيدي علوان",
                    "قصور الساف"
                ]
            ],
            [
                "Delegations" => [
                    "Sfax Ville",
                    "Sfax Ouest",
                    "Sakiet Ezzit",
                    "Sakiet Eddaier",
                    "Sfax sud",
                    "Tina",
                    "Agareb",
                    "Jebeniana ",
                    "El Amra",
                    "El Hencha ",
                    "Menzel chaker",
                    "Ghraiba",
                    "Bir Ali Ben Khelifa",
                    "Sekhira",
                    "Mahrès",
                    "Kerkennah"
                ],
                "Code" => "34",
                "Nbre" => "16",
                "Gouvernorat" => "Sfax",
                "GouvernoratAr" => "صفاقس",
                "DelegationsAr" => [
                    "صفاقـس المدينة",
                    "صفاقـس الغربية",
                    "ساقية الزيت",
                    "ساقية الداير",
                    "صفاقس الجنوبية",
                    "طينة",
                    "عقارب",
                    "جبنيانة",
                    "العامرة",
                    "الحنشة",
                    "منزل شاكر",
                    "الغريبة",
                    "بئر علي بن خليفة",
                    "الصخيرة",
                    "المحرس",
                    "قـرقنـة"
                ]
            ],
            [
                "Delegations" => [
                    "Béja nord",
                    "Béja sud",
                    "Amdoun",
                    "Nefza",
                    "Teboursouk",
                    "Tibar",
                    "Testour",
                    "Goubellat",
                    "Mejez El Bab"
                ],
                "Code" => "21",
                "Nbre" => "9",
                "Gouvernorat" => "Béja",
                "GouvernoratAr" => "باجة",
                "DelegationsAr" => [
                    "باجة الشمالية",
                    "باجة الجنوبية",
                    "عمدون",
                    "نفزة",
                    "تبرسق",
                    "تيبار",
                    "تستور",
                    "قبلاط",
                    "مجاز الباب"
                ]
            ],
            [
                "Delegations" => [
                    "Jendouba",
                    "Jendouba Nord",
                    "Boussalem",
                    "Tabarka",
                    "Ain Drahem",
                    "Fernana",
                    "Ghardimaou",
                    "Oued Mliz",
                    "Balta Bouaouene"
                ],
                "Code" => "22",
                "Nbre" => "9",
                "Gouvernorat" => "Jendouba",
                "GouvernoratAr" => "جندوبة",
                "DelegationsAr" => [
                    "جنـدوبة",
                    "جنـدوبة الشمالية",
                    "بوسالم",
                    "طبرقـة",
                    "عين دراهم",
                    "فرنانة",
                    "غار الدماء",
                    "وادي مليز",
                    "بلطة بوعوان"
                ]
            ],
            [
                "Delegations" => [
                    "Kef Ouest",
                    "Kef Est",
                    "Nebeur",
                    "Sakiet Sidi Youssef",
                    "Tejerouine",
                    "Kalâat sinane",
                    "Kalâa El khasba",
                    "Jerissa",
                    "Gsour",
                    "Dahmani",
                    "Le Sers"
                ],
                "Code" => "23",
                "Nbre" => "11",
                "Gouvernorat" => "Kef",
                "GouvernoratAr" => "الكاف",
                "DelegationsAr" => [
                    "الكاف الغربية",
                    "الكاف الشرقية",
                    "نبـر",
                    "ساقية سيدي يوسف",
                    "تاجروين",
                    "قلعة سنان",
                    "القلعة الخصبة",
                    "الجريصة",
                    "القصور",
                    "الدهماني",
                    "السرس"
                ]
            ],
            [
                "Delegations" => [
                    "Siliana nord",
                    "Siliana sud",
                    "Bouarada ",
                    "Gâafour",
                    "El Aroussa",
                    "Le Krib",
                    "Bourouis",
                    "Makther",
                    "Rouhia",
                    "Kesra",
                    "Bargou"
                ],
                "Code" => "24",
                "Nbre" => "11",
                "Gouvernorat" => "Siliana",
                "GouvernoratAr" => "سليانة",
                "DelegationsAr" => [
                    "سليانة الشمالية",
                    "سليانة الجنوبية",
                    "بوعرادة",
                    "قعفور",
                    "العروسة",
                    "الكريب",
                    "بورويس",
                    "مكثر",
                    "الروحية",
                    "كسرى",
                    "برقو"
                ]
            ],
            [
                "Delegations" => [
                    "Kairouan Nord ",
                    "Kairouan Sud",
                    "Chebika",
                    "Sebikha",
                    "Oueslatia",
                    "Haffouz",
                    "El Ala",
                    "Hajeb El Ayoun",
                    "Nasrallah",
                    "Cherarda",
                    "Bouhajla"
                ],
                "Code" => "41",
                "Nbre" => "11",
                "Gouvernorat" => "Kairouan",
                "GouvernoratAr" => "القيروان",
                "DelegationsAr" => [
                    "القيروان الشمالية",
                    "القيروان الجنوبية",
                    "الشبيكة",
                    "السبيخة",
                    "الوسلاتية",
                    "حفوز",
                    "العلا",
                    "حاجب العيون",
                    "نصر الله",
                    "الشراردة",
                    "بوحجلة"
                ]
            ],
            [
                "Delegations" => [
                    "Sidi Bouzid Ouest",
                    "Sidi Bouzid Est",
                    "Jelma",
                    "Sabbalet Ouled Askar",
                    "Bir Hfay",
                    "Sidi Ali Benôun ",
                    "Menzel Bouzayane",
                    "Meknassi",
                    "Souk Jedid",
                    "Mezouna",
                    "Regueb",
                    "Ouled Haffouz"
                ],
                "Code" => "43",
                "Nbre" => "12",
                "Gouvernorat" => "Sidi Bouzid",
                "GouvernoratAr" => "سيدي بوزيد",
                "DelegationsAr" => [
                    "سيدي بوزيد الغربية",
                    "سيدي بوزيد الشرقية",
                    "جلمة",
                    "سبالة أولاد عسكر",
                    "بئر الحفي",
                    "سيدي علي بن عون",
                    "منزل بوزيان",
                    "المكناسي",
                    "سوق الجديد",
                    "المزونة",
                    "الرقاب",
                    "أولاد حفوز"
                ]
            ],
            [
                "Delegations" => [
                    "Kasserine Nord",
                    "Kasserine Sud",
                    "Azzouhour",
                    "Hassi ferid",
                    "Sbitla",
                    "Sbiba",
                    "Jedliane",
                    "El Ayoun",
                    "Tela",
                    "Hidra",
                    "Foussana",
                    "Feriana",
                    "Mejel Bel Abbes"
                ],
                "Code" => "42",
                "Nbre" => "13",
                "Gouvernorat" => "Kasserine",
                "GouvernoratAr" => "القصرين",
                "DelegationsAr" => [
                    "القصرين الشمالية",
                    "القصرين الجنوبية",
                    "الزهور",
                    "حاسي الفريد",
                    "سبيطلة",
                    "سبيبة",
                    "جدليان",
                    "العيون",
                    "تالة",
                    "حيدرة",
                    "فوسانة",
                    "فريانة",
                    "ماجل بلعباس"
                ]
            ],
            [
                "Delegations" => [
                    "Gabès ville",
                    "Gabès ouest",
                    "Gabès sud",
                    "Ghannouch",
                    "Metouia",
                    "Menzel habib",
                    "Hamma",
                    "Matmata",
                    "Matmata nouvelle",
                    "Mareth"
                ],
                "Code" => "51",
                "Nbre" => "10",
                "Gouvernorat" => "Gabès",
                "GouvernoratAr" => "قابس",
                "DelegationsAr" => [
                    "قابـس المدينة",
                    "قابـس الغربية",
                    "قابـس الجنوبية",
                    "غنوش",
                    "المطوية",
                    "منزل الحبيب",
                    "الحامة",
                    "مطماطة",
                    "مطماطة الجديدة",
                    "مارث"
                ]
            ],
            [
                "Delegations" => [
                    "Mednine Nord",
                    "Mednine Sud",
                    "Béni khedach",
                    "Ben Guerdene",
                    "Zazis",
                    "Jerba Houmet Souk",
                    "Jerba Midoun",
                    "Jerba Ajim",
                    "Sidi Makhlouf"
                ],
                "Code" => "52",
                "Nbre" => "9",
                "Gouvernorat" => "Medenine",
                "GouvernoratAr" => "مدنين",
                "DelegationsAr" => [
                    "مدنيـن الشمالية",
                    "مدنين الجنوبية",
                    "بني خداش",
                    "بن قردان",
                    "جرجيس",
                    "جربة حومة السوق",
                    "جربة ميدون",
                    "جربة أجيم",
                    "سيدي مخلوف"
                ]
            ],
            [
                "Delegations" => [
                    "Gafsa Nord",
                    "Sidi Aich",
                    "El Ksar",
                    "Gafsa Sud",
                    "Moulares",
                    "Redyef",
                    "Métlaoui",
                    "El Mdhilla",
                    "El Guettar",
                    "Belkhir",
                    "Sned"
                ],
                "Code" => "61",
                "Nbre" => "11",
                "Gouvernorat" => "Gafsa",
                "GouvernoratAr" => "قفصة",
                "DelegationsAr" => [
                    "قفصة الشمالية",
                    "سيدي عيش",
                    "القصر",
                    "قفصة الجنوبية",
                    "أم العرائس",
                    "الرديف",
                    "المتلوي",
                    "المظيلة",
                    "القطار",
                    "بلخير",
                    "السند"
                ]
            ],
            [
                "Delegations" => [
                    "Tozeur",
                    "Degueche",
                    "Tameghza",
                    "Nefta",
                    "Hezoua"
                ],
                "Code" => "62",
                "Nbre" => "5",
                "Gouvernorat" => "Tozeur",
                "GouvernoratAr" => "توزر",
                "DelegationsAr" => [
                    "توزر",
                    "دقاش",
                    "تمغزة",
                    "نفطة",
                    "حزوة"
                ]
            ],
            [
                "Delegations" => [
                    "Tataouine Nord",
                    "Tataouine Sud",
                    "Smar",
                    "Bir Lahmer",
                    "Ghomrassen",
                    "Dhehiba",
                    "Remada"
                ],
                "Code" => "53",
                "Nbre" => "7",
                "Gouvernorat" => "Tataouine",
                "GouvernoratAr" => "تطاوين",
                "DelegationsAr" => [
                    "تطاوين الشمالية",
                    "تطاوين الجنوبية",
                    "الصمار",
                    "البئر الأحمر",
                    "غمراسن",
                    "ذهيبة",
                    "رمادة"
                ]
            ],
            [
                "Delegations" => [
                    "Kébili Sud",
                    "Kébili Nord",
                    "Souk El Ahad",
                    "Douz nord",
                    "Douz sud",
                    "El Faouar"
                ],
                "Code" => "63",
                "Nbre" => "6",
                "Gouvernorat" => "Kébili",
                "GouvernoratAr" => "قبلي",
                "DelegationsAr" => [
                    "قبلي الجنوبية",
                    "قبلي الشمالية",
                    "سوق الأحد",
                    "دوز الشمالية",
                    "دوز الجنوبية ",
                    "الفوار"
                ]
            ]
        ];

        foreach ($govDelArray as $gov) {

            $gouvernorat = Governorate::create(
                [
                    'code' => intval($gov['Code']),
                    'nom_en' => $gov['Gouvernorat'],
                    'nom_fr' => $gov['Gouvernorat'],
                    'nom_ar' => $gov['GouvernoratAr']
                ]
            );

            foreach ($gov['Delegations'] as $key => $delegation) {
                $gouvernorat->delegations()->create(
                    [
                        'nom_en' => $delegation, 
                        'nom_fr' => $delegation, 
                        'nom_ar' => $gov['DelegationsAr'][$key]
                    ]
                );
            }
        }

    }
}
