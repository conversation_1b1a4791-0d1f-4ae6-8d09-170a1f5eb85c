<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCardFeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'id_subs_type' => 'required|exists:subs_types,id',
            'date_start' => 'required|date',
            'date_end' => 'required|date|after:date_start'
        ];
    }
}