<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAffectationCardTypeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_agent' => 'required|exists:admins,id',
            'cardTypes' => 'required|array',
            'cardTypes.*' => 'required|exists:card_types,id',
            'ranges' => 'required|array',
            'ranges.*.cardType' => 'required|exists:card_types,id',
            'ranges.*.start_serial_number' => 'required|integer',
            'ranges.*.end_serial_number' => 'required|integer|gt:ranges.*.start_serial_number',
        ];
    }
}

