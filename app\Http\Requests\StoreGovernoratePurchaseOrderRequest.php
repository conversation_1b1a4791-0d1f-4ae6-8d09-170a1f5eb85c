<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreGovernoratePurchaseOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ref' => 'required|string|max:255|unique:governorate_purchase_orders,ref',
            'initial_amount' => 'required|numeric|min:0',
            'status' => 'required|boolean',
            'date' => 'required|date',
            'id_governorate' => 'required|exists:governorates,id'
        ];
    }
}
