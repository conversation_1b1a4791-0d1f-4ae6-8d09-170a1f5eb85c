<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TypeVehicleTypeLocation;

class TypeVehicleTypeLocationPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_type_vehicle_type_locations');
    }

    public function view(Admin $admin, TypeVehicleTypeLocation $typeVehicleTypeLocation): bool
    {
        return $admin->hasPermissionTo('view_type_vehicle_type_locations');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_type_vehicle_type_locations');
    }

    public function update(Admin $admin, TypeVehicleTypeLocation $typeVehicleTypeLocation): bool
    {
        return $admin->hasPermissionTo('edit_type_vehicle_type_locations');
    }

    public function delete(Admin $admin, TypeVehicleTypeLocation $typeVehicleTypeLocation): bool
    {
        return $admin->hasPermissionTo('delete_type_vehicle_type_locations');
    }
}
