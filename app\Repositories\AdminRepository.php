<?php

namespace App\Repositories;

use App\Models\Admin;
use Prettus\Repository\Eloquent\BaseRepository;

class AdminRepository extends BaseRepository
{
    protected $cacheKey = '';
    
    public function model(): string
    {
        return Admin::class;
    }
    
    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
    
    protected $fieldSearchable = [
        'matricule' => 'like',
        'lastname' => 'like',
        'firstname' => 'like',
        'phone' => 'like',
        'cin' => 'like',
        'email' => 'like'
    ];
}

