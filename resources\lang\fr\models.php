<?php

return [
    // Model names for audit system
    'admin' => 'Administrateur',
    'client' => 'Client',
    'subscription' => 'Abonnement',
    'transaction' => 'Transaction',
    'agency' => 'Agence',
    'delegation' => 'Délégation',
    'governorate' => 'Gouvernorat',
    'establishment' => 'Établissement',
    'type_establishment' => 'Type d\'établissement',
    'degree' => 'Degré',
    'payment_method' => 'Mode de paiement',
    'card_type' => 'Type de carte',
    'subs_type' => 'Type d\'abonnement',
    'tariff_base' => 'Base tarifaire',
    'line' => 'Ligne',
    'station' => 'Station',
    'season' => 'Saison',
    'trip' => 'Trajet',
    'tariff_option' => 'Option tarifaire',
    'campaign' => 'Campagne',
    'sale_period' => 'Période de vente',
    'sale_point' => 'Point de vente',
    'affectation_agent' => 'Affectation agent',
    'type_client' => 'Type de client',
    'periodicity' => 'Périodicité',
    'discount' => 'Remise',
    'card_fee' => 'Frais de carte',
    'location_type' => 'Type de location',
    'location_season' => 'Saison de location',
    'type_vehicule' => 'Type de véhicule',
    'type_vehicle_type_location' => 'Type véhicule - Type location',
    'type_vehicule_saison_location' => 'Type véhicule - Saison location',
    'social_affair' => 'Affaire sociale',
    'academic_year' => 'Année académique',
    'subs_duplication' => 'Duplication d\'abonnement',
    'stock_card' => 'Stock de cartes',
    'subs_card' => 'Carte d\'abonnement',
    'card_sequence' => 'Séquence de carte',
    'motif_duplicate' => 'Motif de duplication',
    'governorate_purchase_order' => 'Bon de commande gouvernorat',
    'affectation_card_type' => 'Affectation type de carte',
    'config' => 'Configuration',
    'audit' => 'Audit',

    // Event types
    'events' => [
        'created' => 'Créé',
        'updated' => 'Modifié',
        'deleted' => 'Supprimé',
        'restored' => 'Restauré'
    ],

    // Field names commonly used in audits
    'fields' => [
        'id' => 'ID',
        'nom_fr' => 'Nom (FR)',
        'nom_en' => 'Nom (EN)',
        'nom_ar' => 'Nom (AR)',
        'firstname' => 'Prénom',
        'lastname' => 'Nom de famille',
        'email' => 'Email',
        'phone' => 'Téléphone',
        'address' => 'Adresse',
        'status' => 'Statut',
        'amount' => 'Montant',
        'date' => 'Date',
        'created_at' => 'Créé le',
        'updated_at' => 'Modifié le',
        'start_date' => 'Date de début',
        'end_date' => 'Date de fin',
        'code' => 'Code',
        'ref' => 'Référence',
        'is_active' => 'Actif',
        'is_moral' => 'Personne morale',
        'society_name' => 'Nom de société',
        'identity_number' => 'Numéro d\'identité',
        'dob' => 'Date de naissance'
    ]
];
