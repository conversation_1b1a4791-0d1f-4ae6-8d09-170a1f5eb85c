<?php

namespace Database\Seeders;

use App\Models\Station;
use Illuminate\Database\Seeder;

class MissedStationSeeder extends Seeder
{
    public function run(): void
    {
    
        // Read from missed_stations.txt file if it exists
        $filePath = database_path('seeders/missed_stations.txt');
        $fileStations = [];

        if (file_exists($filePath)) {
            $fileContent = file_get_contents($filePath);
            $fileStations = array_filter(explode("\n", $fileContent));
            echo "Found " . count($fileStations) . " stations in missed_stations.txt\n";
        }

        // Combine predefined and file stations
        $allMissedStations = [];

        // Add stations from file (with basic translations)
        foreach ($fileStations as $stationName) {
            $stationName = trim($stationName);
            if (!empty($stationName) && !isset($allMissedStations[$stationName])) {
                $allMissedStations[$stationName] = [
                    'nom_fr' => $stationName,
                    'nom_en' => $stationName, // Default to French name
                    'nom_ar' => '', // Empty Arabic name for file-based stations
                ];
            }
        }

        $addedCount = 0;
        $existingCount = 0;

        foreach ($allMissedStations as $stationData) {
            // Check if station already exists
            $existingStation = Station::where('nom_fr', $stationData['nom_fr'])->first();

            if (!$existingStation) {
                Station::create([
                    'nom_fr' => trim($stationData['nom_fr']),
                    'nom_en' => trim($stationData['nom_en']),
                    'nom_ar' => trim($stationData['nom_ar']),
                    'longitude' => 0,
                    'latitude' => 0,
                    'id_delegation' => null,
                    'id_governorate' => null
                ]);

                echo "Added missing station: {$stationData['nom_fr']}\n";
                $addedCount++;
            } else {
                echo "Station already exists: {$stationData['nom_fr']}\n";
                $existingCount++;
            }
        }

        echo "\nSummary:\n";
        echo "- Added: {$addedCount} stations\n";
        echo "- Already existed: {$existingCount} stations\n";
        echo "- Total processed: " . ($addedCount + $existingCount) . " stations\n";
    }
}
