<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('type_vehicule_saison_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_type_vehicule')->constrained('type_vehicules');
            $table->foreignId('id_saison_location')->constrained('seasons');
            $table->decimal('prix_km', 10, 2)->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();

            $table->unique(['id_type_vehicule', 'id_saison_location'], 'vehicle_seasons_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('type_vehicule_saison_locations');
    }
};
