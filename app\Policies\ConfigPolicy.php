<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Config;
use Illuminate\Auth\Access\HandlesAuthorization;

class ConfigPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_configs');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Admin $admin, Config $config): bool
    {
        return $admin->hasPermissionTo('view_configs') || ($config->is_public && $admin->hasPermissionTo('view public configs'));
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_configs');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Admin $admin, Config $config): bool
    {
        if ($config->is_system) {
            return $admin->hasPermissionTo('update system configs');
        }
        
        return $admin->hasPermissionTo('update_configs');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Admin $admin, Config $config): bool
    {
        if ($config->is_system) {
            return $admin->hasPermissionTo('delete system configs');
        }
        
        return $admin->hasPermissionTo('delete_configs');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(Admin $admin, Config $config): bool
    {
        return $admin->hasPermissionTo('restore configs');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(Admin $admin, Config $config): bool
    {
        return $admin->hasPermissionTo('force delete configs');
    }
}
