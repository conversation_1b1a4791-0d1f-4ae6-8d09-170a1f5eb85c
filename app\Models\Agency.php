<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Agency extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'code',
        'contact',
        'address',
        'id_delegation',
        'id_governorate'
    ];

    /**
     * Get the delegation that owns the agency.
     */
    public function delegation(): BelongsTo
    {
        return $this->belongsTo(Delegation::class, 'id_delegation');
    }

    /**
     * Get the governorate that owns the agency.
     */
    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'id_governorate');
    }

    /**
     * Get the sale points for the agency.
     */
    public function salePoints(): HasMany
    {
        return $this->hasMany(SalePoint::class, 'id_agency');
    }
}

