<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

use OwenIt\Auditing\Contracts\Auditable;

class AffectationAgent extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'id_agent',
        'id_sale_point',
        'id_sale_period',
        'date_start',
        'date_end'
    ];

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'id_agent');
    }

    public function salePoint(): BelongsTo
    {
        return $this->belongsTo(SalePoint::class, 'id_sale_point');
    }

    public function salePeriod(): BelongsTo
    {
        return $this->belongsTo(SalePeriod::class, 'id_sale_period');
    }

    public function paymentMethods(): BelongsToMany
    {
        return $this->belongsToMany(PaymentMethod::class, 'affectation_agent_payment_methods', 
            'id_affectation_agent', 'id_payment_method')
            ->withTimestamps();
    }
}
