<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class SubsCard extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'ref',
        'id_card_type',
        'id_subscription',
        'id_motif_duplicate',
        'id_sale_point',
        'id_affectation_card_type',
        'duplicate_amount'
    ];

    public function affectationCardType(): BelongsTo
    {
        return $this->belongsTo(AffectationCardType::class, 'id_affectation_card_type');
    }

    public function cardType(): BelongsTo
    {
        return $this->belongsTo(CardType::class, 'id_card_type');
    }

    public function salePoint(): BelongsTo
    {
        return $this->belongsTo(SalePoint::class, 'id_sale_point');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'id_subscription');
    }

    public function motifDuplicate(): BelongsTo
    {
        return $this->belongsTo(MotifDuplicate::class, 'id_motif_duplicate');
    }
}
