<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalePeriodResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'date_start_client' => $this->date_start_client,
            'date_end_client' => $this->date_end_client,
            'date_start_agent' => $this->date_start_agent,
            'date_end_agent' => $this->date_end_agent,
            'date_start_validity' => $this->date_start_validity,
            'date_end_validity' => $this->date_end_validity,
            'id_periodicity' => $this->id_periodicity,
            'id_campaign' => $this->id_campaign,
            'id_abn_type' => $this->whenLoaded('campaign', function() {
                return $this->campaign->id_abn_type;
            }),
            'status' => $this->status,
            'periodicity' => new PeriodicityResource($this->whenLoaded('periodicity')),
            'campaign' => new CampaignResource($this->whenLoaded('campaign')),
            'abn_type' => new SubsTypeResource($this->whenLoaded('campaign.abnType', function() {
                return $this->campaign->abnType;
            })),
            //'affectation_agents' => AffectationAgentResource::collection($this->whenLoaded('affectationAgents')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

