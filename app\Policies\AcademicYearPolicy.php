<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\AcademicYear;
use Illuminate\Auth\Access\HandlesAuthorization;

class AcademicYearPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_academic_years');
    }

    public function view(Admin $admin, AcademicYear $academicYear): bool
    {
        return $admin->can('view_academic_years');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_academic_years');
    }

    public function update(Admin $admin, AcademicYear $academicYear): bool
    {
        return $admin->can('edit_academic_years');
    }

    public function delete(Admin $admin, AcademicYear $academicYear): bool
    {
        return $admin->can('delete_academic_years');
    }
}
