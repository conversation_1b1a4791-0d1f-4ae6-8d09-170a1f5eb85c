<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubsCardResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'ref' => $this->ref,
            'id_card_type' => $this->id_card_type,
            'id_subscription' => $this->id_subscription,
            'id_motif_duplicate' => $this->id_motif_duplicate,
            'id_sale_point' => $this->id_sale_point,
            'id_sale_period' => $this->id_sale_period,
            'id_affectation_card_type' => $this->id_affectation_card_type,
            'duplicate_amount' => $this->duplicate_amount,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relations
            'sale_point' => new SalePointResource($this->whenLoaded('salePoint')),
            'card_type' => new CardTypeResource($this->whenLoaded('cardType')),
            'motif_duplicate' => new MotifDuplicateResource($this->whenLoaded('motifDuplicate')),
        ];
    }
}

