<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class TariffBase extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_subs_type',
        'is_regular',
        'is_triff_fixed',
        'km_start',
        'km_end'
    ];

    protected $casts = [
        'is_regular' => 'boolean',
        'is_triff_fixed' => 'boolean'
    ];

    public function tariffs(): HasMany
    {
        return $this->hasMany(Tariff::class, 'id_tariff_base');
    }

    public function tariffOptions(): HasMany
    {
        return $this->hasMany(TariffOption::class, 'id_tariff_base');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }
}





