<?php

namespace Database\Seeders;

use App\Models\SubsType;
use App\Models\TariffBase;
use Illuminate\Database\Seeder;

class TariffBasesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        
        $tariffBasesScolaire = [
            [
                'nom_fr' => 'Tarif de base scolaire - 1',
                'nom_en' => 'School Base Tariff - 1',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 1',
                'tariff' => 28700,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 2',
                'nom_en' => 'School Base Tariff - 2',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 2',
                'tariff' => 41400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 3',
                'nom_en' => 'School Base Tariff - 3',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 3',
                'tariff' => 23400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 4',
                'nom_en' => 'School Base Tariff - 4',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 4',
                'tariff' => 35400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 5',
                'nom_en' => 'School Base Tariff - 5',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 5',
                'tariff' => 48700,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 6',
                'nom_en' => 'School Base Tariff - 6',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 6',
                'tariff' => 53400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - 7',
                'nom_en' => 'School Base Tariff - 7',
                'nom_ar' => 'التعريفة الأساسية المدرسية - 7',
                'tariff' => 17400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 1 
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 1',
                'nom_en' => 'University Base Tariff - 1',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 1',
                'tariff' => 28700,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 2',
                'nom_en' => 'University Base Tariff - 2',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 2',
                'tariff' => 41400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 3',
                'nom_en' => 'University Base Tariff - 3',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 3',
                'tariff' => 23400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 4',
                'nom_en' => 'University Base Tariff - 4',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 4',
                'tariff' => 35400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 5',
                'nom_en' => 'University Base Tariff - 5',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 5',
                'tariff' => 48700,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 6',
                'nom_en' => 'University Base Tariff - 6',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 6',
                'tariff' => 53400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - 7',
                'nom_en' => 'University Base Tariff - 7',
                'nom_ar' => 'التعريفة الأساسية الجامعية - 7',
                'tariff' => 17400,
                'is_triff_fixed' => null,
                'km_start' => null,
                'km_end' => null,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => false,
                'subs_type_id' => 2
            ],
        ];


        foreach ($tariffBasesScolaire as $tariffBaseData) {
            $tariffBase = TariffBase::create([
                'nom_fr' => $tariffBaseData['nom_fr'],
                'nom_en' => $tariffBaseData['nom_en'],
                'nom_ar' => $tariffBaseData['nom_ar'],
                'is_triff_fixed' => $tariffBaseData['is_triff_fixed'],
                'km_start' => $tariffBaseData['km_start'],
                'km_end' => $tariffBaseData['km_end'],
                'is_regular' => $tariffBaseData['is_regular'],
                'id_subs_type' => $tariffBaseData['subs_type_id']
            ]);

            // Create the associated tariff
            $tariffBase->tariffs()->create([
                'tariff' => $tariffBaseData['tariff'] / 1000,
                'date_subscription' => $tariffBaseData['date_subscription'],
                'date_website' => $tariffBaseData['date_website']
            ]);
        }


        $tariffBasesCivil = [
            [
                'nom_fr' => 'Tarif de base civile - 1',
                'nom_en' => 'Civil Base Tariff - 1',
                'nom_ar' => 'التعريفة الأساسية المدنية - 1',
                'tariff' => 650,
                'is_triff_fixed' => true,
                'km_start' => 0,
                'km_end' => 10,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => true,
                'subs_type_id' => 3
            ],
            [
                'nom_fr' => 'Tarif de base civile - 2',
                'nom_en' => 'Civil Base Tariff - 2',
                'nom_ar' => 'التعريفة الأساسية المدنية - 2',
                'tariff' => 65,
                'is_triff_fixed' => false,
                'km_start' => 11,
                'km_end' => 150,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-01',
                'is_regular' => true,
                'subs_type_id' => 3
            ],
            [
                'nom_fr' => 'Tarif de base civile - 3',
                'nom_en' => 'Civil Base Tariff - 3',
                'nom_ar' => 'التعريفة الأساسية المدنية - 3',
                'tariff' => 55,
                'is_triff_fixed' => false,
                'km_start' => 151,
                'km_end' => 260,
                'date_subscription' => '2025-01-01',
                 'date_website' => '2025-01-01',
                'is_regular' => true,
                'subs_type_id' => 3
            ]
        ];

        foreach ($tariffBasesCivil as $tariffBaseData) {
            $tariffBase = TariffBase::create([
                'nom_fr' => $tariffBaseData['nom_fr'],
                'nom_en' => $tariffBaseData['nom_en'],
                'nom_ar' => $tariffBaseData['nom_ar'],
                'is_triff_fixed' => $tariffBaseData['is_triff_fixed'],
                'km_start' => $tariffBaseData['km_start'],
                'km_end' => $tariffBaseData['km_end'],
                'is_regular' => $tariffBaseData['is_regular'],
                'id_subs_type' => $tariffBaseData['subs_type_id']
            ]);

            // Create the associated tariff
            $tariffBase->tariffs()->create([
                'tariff' => $tariffBaseData['tariff'] / 1000,
                'date_subscription' => $tariffBaseData['date_subscription'],
                'date_website' => $tariffBaseData['date_website']
            ]);
        }
    }
}

