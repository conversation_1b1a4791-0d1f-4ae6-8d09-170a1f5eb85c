<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subs_duplications', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('motif_duplicate_id')->unsigned();
            $table->bigInteger('admin_id')->unsigned();
            $table->foreign('motif_duplicate_id')->references('id')->on('motif_duplicates')->onDelete('cascade');
            $table->foreign('admin_id')->references('id')->on('admins')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subs_duplications');
    }
};
