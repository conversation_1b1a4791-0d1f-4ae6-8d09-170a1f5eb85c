<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetBusLocationAmountRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_location_type' => 'required|exists:location_types,id',
            'id_vehicule_type' => 'required|exists:type_vehicules,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
            'number_of_km' => 'required|numeric|min:0'
        ];
    }
}

