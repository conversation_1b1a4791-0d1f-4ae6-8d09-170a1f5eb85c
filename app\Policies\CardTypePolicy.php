<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\CardType;

class CardTypePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_card_types');
    }

    public function view(Admin $admin, CardType $cardType): bool
    {
        return $admin->can('view_card_types');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_card_types');
    }

    public function update(Admin $admin, CardType $cardType): bool
    {
        return $admin->can('edit_card_types');
    }

    public function delete(Admin $admin, CardType $cardType): bool
    {
        return $admin->can('delete_card_types');
    }
}