<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use OwenIt\Auditing\Contracts\Auditable;

class Option extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use \Illuminate\Database\Eloquent\Factories\HasFactory;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar'
    ];

    public function seasons(): BelongsToMany
    {
        return $this->belongsToMany(Season::class, 'option_seasons');
    }
}
