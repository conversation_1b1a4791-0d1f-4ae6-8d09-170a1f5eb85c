<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class WebsiteTrip extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_line',
        'id_station_start',
        'id_station_end',
        'status',
        'number_of_km',
    ];

    protected $casts = [
        'status' => 'boolean',
        'number_of_km' => 'integer',
    ];

    /**
     * Get the line that owns the trip.
     */
    public function line(): BelongsTo
    {
        return $this->belongsTo(Line::class, 'id_line');
    }

    /**
     * Get the starting station of the trip.
     */
    public function startStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_start');
    }

    /**
     * Get the ending station of the trip.
     */
    public function endStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_end');
    }
}
