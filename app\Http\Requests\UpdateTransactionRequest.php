<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTransactionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'subscription_id' => 'sometimes|exists:subscriptions,id',
            'client_id' => 'sometimes|exists:clients,id',
            'amount' => 'sometimes|numeric|min:0',
            'payment_date' => 'nullable|date',
            'payment_mode' => 'sometimes|string|max:20|in:en_ligne,guichet',
            'payment_method_id' => 'sometimes|exists:payment_methods,id',
            'status' => 'sometimes|string|max:20|in:completed,pending,failed,refunded',
            'transaction_reference' => 'nullable|string|max:255',
            'online_gateway' => 'nullable|string|max:50',
            'sale_point_id' => 'nullable|exists:sale_points,id',
            'employee_id' => 'nullable|exists:admins,id',
            'online_user_agent' => 'nullable|string',
            'notes' => 'nullable|string'
        ];
    }
}
