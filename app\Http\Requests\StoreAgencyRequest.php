<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAgencyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:agencies,code',
            'contact' => 'required|string|max:255|unique:agencies,contact',
            'address' => 'required|string|max:255',
            'id_delegation' => 'required|exists:delegations,id',
            'id_governorate' => 'required|exists:governorates,id'
        ];
    }
}

