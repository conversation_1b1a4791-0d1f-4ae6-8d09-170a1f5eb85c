<?php

namespace Database\Seeders;

use App\Models\Governorate;
use App\Models\SocialAffair;
use Illuminate\Database\Seeder;

class SocialAffairSeeder extends Seeder
{
    public function run(): void
    {
        $governorates = Governorate::all();

        if ($governorates->isEmpty()) {
            $this->command->info('No governorates found. Please run the GovernorateDelegationSeeder first.');
            return;
        }

        // Arrays of possible values
        $typeEleveEtudiant = [
            'eleve',
            'etudiant'
        ];
        $typeSociete = [
            'Entreprise privée',
            'Institution publique',
            'Association',
            'Organisation à but non lucratif',
            'Start-up'
        ];

        $niveauxEtude = [
            'Primaire',
            'Collège',
            'Lycée',
            'Licence',
            'Master',
            'Formation professionnelle'
        ];

        $trajets = [
            'Route Tunis-Nabeul',
            'Route Hammamet-Tunis',
            'Route Nabeul-Kairouan',
            'Route Locale',
            'Route Express',
            'Circuit Scolaire'
        ];

        // Generate 100 records
        $socialAffairs = [];

        for ($i = 0; $i < 100; $i++) {
            $isStudent = rand(0, 1) === 1;
            $governorate = $governorates->random();
            $delegation = $governorate->delegations->random();

            $socialAffairs[] = [
                'governorate_id' => $governorate->id,
                'delegation' => $delegation->nom_fr,
                'eleve_etudiant' => $isStudent ? $typeEleveEtudiant[array_rand($typeEleveEtudiant)] : null,
                'societe' => !$isStudent ? $typeSociete[array_rand($typeSociete)] : '',
                'nom_parent' => "Parent_" . fake()->lastName(),
                'cin_parent' => fake()->numerify('########'),
                'identifier' => fake()->numerify('########'),
                'dob' => fake()->dateTimeBetween('-50 years', '-10 years')->format('Y-m-d'),
                'telephone' => fake()->numerify('########'),
                'nom_complet' => fake()->name(),
                'niveau_etude' => $isStudent ?
                    $niveauxEtude[array_rand($niveauxEtude)] :
                    'Non applicable',
                'trajet_requise' => $trajets[array_rand($trajets)],
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        // Insert in chunks for better performance
        foreach (array_chunk($socialAffairs, 20) as $chunk) {
            SocialAffair::insert($chunk);
        }
    }
}

