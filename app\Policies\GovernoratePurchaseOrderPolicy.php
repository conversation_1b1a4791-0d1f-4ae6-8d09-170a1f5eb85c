<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\GovernoratePurchaseOrder;

class GovernoratePurchaseOrderPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, GovernoratePurchaseOrder $governoratePurchaseOrder): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, GovernoratePurchaseOrder $governoratePurchaseOrder): bool
    {
        return true;
    }

    public function delete(Admin $admin, GovernoratePurchaseOrder $governoratePurchaseOrder): bool
    {
        return true;
    }
}