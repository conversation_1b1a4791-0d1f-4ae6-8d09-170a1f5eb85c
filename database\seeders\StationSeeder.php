<?php

namespace Database\Seeders;

use App\Models\Line;
use App\Models\Station;
use App\Models\LineStation;

use Illuminate\Database\Seeder;


class StationSeeder extends Seeder
{
    public function run(): void
    {
        include_once('data.php');
        // First, create all stations with French names
        foreach ($station_all as $station) {
            Station::create([
                'nom_fr' => trim($station['CODE_ARRET']),
                'nom_en' => trim($station['CODE_ARRET']),
                'nom_ar' => '',
                'longitude' => 0,
                'latitude' => 0,
                'id_delegation' => null,
                'id_governorate' => null
            ]);
        }

        // Then update Arabic names using the mapping from station_line_arab
        $arabicNames = [];
        foreach ($station_line as $frStation) {
            foreach ($station_line_arab as $arStation) {
                if ($frStation['CODE_LIGNE'] === $arStation['CODE_LIGNE'] &&
                    $frStation['NUMERO'] === $arStation['NUMERO']) {
                    $arabicNames[$frStation['CODE_ARRET']] = $arStation['CODE_ARRET'];
                    break;
                }
            }
        }

        // Update Arabic names for stations that have translations
        foreach ($arabicNames as $frName => $arName) {
            Station::where('nom_fr', trim($frName))
                  ->update(['nom_ar' => trim($arName)]);
        }

        /*-------------------------------------------------------------------------
        ---- insert LineStation from $station_line (find station by CODE_ARRET)
        ----------------------------------------------------------------------------*/
        // create unique array from $station_line (CODE_LINE, CODE_ARRET) and get HEURE_DEPART in array
        $unique_station_line = [];
        foreach ($station_line as $station) {
            $key = $station['CODE_LIGNE'] . '_' . $station['CODE_ARRET'];

            if (!isset($unique_station_line[$key])) {
                $unique_station_line[$key] = [
                    'CODE_LIGNE' => $station['CODE_LIGNE'],
                    'CODE_ARRET' => $station['CODE_ARRET'],
                    'NUMERO' => $station['NUMERO'],
                    'OPTIONS' => []
                ];
            }

            if (!empty($station['HEURE_DEPART'])) {
                $unique_station_line[$key]['OPTIONS'][] = [
                    'HEURE_DEPART' => $station['HEURE_DEPART'],
                    'CODE_OPTION' => isset($station['CODE_OPTION']) ? intval($station['CODE_OPTION']) : NULL,
                    'DIRECTION' => isset($station['DIRECTION']) ? intval($station['DIRECTION']) : NULL,
                    'avancement' => isset($station['avancement']) ? intval($station['avancement']) : NULL
                ];
            }
        }

        //Convert to indexed array if needed
        $unique_station_line = array_values($unique_station_line);

        foreach ($unique_station_line as $station) {
            $lineId = Line::where('CODE_LINE', $station['CODE_LIGNE'])->first()?->id;
            $stationId = Station::where('nom_fr', $station['CODE_ARRET'])->first()?->id;

            if($lineId && $stationId) {
                $hasDepartures = !empty($station['OPTIONS']);
                $departureConfigs = [];
                
                if ($hasDepartures) {
                    foreach ($station['OPTIONS'] as $time) {
                        if (!empty($time)) {
                            $departureConfigs[] = array(
                                'option' => isset($time['CODE_OPTION']) ? intval($time['CODE_OPTION']) : NULL,
                                'advancement_position' => isset($time['avancement']) && $time['avancement'] !== null ? intval($time['avancement']) : NULL,
                                'direction' => isset($time['DIRECTION']) ? intval($time['DIRECTION']) : NULL,
                                'time' => isset($time['HEURE_DEPART']) ? $time['HEURE_DEPART'] : NULL
                            );
                        }
                    }
                }

                LineStation::create([
                    'id_line' => $lineId,
                    'id_station' => $stationId,
                    'position' => $station['NUMERO'] ? $station['NUMERO'] : 1,
                    'type' => 'INTER',
                    'has_departures' => $hasDepartures,
                    'departure_configs' => $hasDepartures ? $departureConfigs : null
                ]);
            }
        }
    }
}



