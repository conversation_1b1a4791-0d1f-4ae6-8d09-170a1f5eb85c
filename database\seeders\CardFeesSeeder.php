<?php

namespace Database\Seeders;

use App\Models\CardFee;
use App\Models\SubsType;
use Illuminate\Database\Seeder;

class CardFeesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all subscription types
        $subsTypes = SubsType::all();
        
        $cardFees = [
            // School Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte scolaire - 2025',
                'nom_en' => 'School Card Fee - 2025',
                'nom_ar' => 'رسوم البطاقة المدرسية - 2025',
                'amount' => 2.00,
                'date_start' => now()->startOfYear(),
                'date_end' => now()->endOfYear(),
                'subs_type_index' => 0 // School subscription
            ],

            // University Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte universitaire - 2025',
                'nom_en' => 'University Card Fee - 2025',
                'nom_ar' => 'رسوم البطاقة الجامعية - 2025',
                'amount' => 2.50,
                'date_start' => now()->startOfYear(),
                'date_end' => now()->endOfYear(),
                'subs_type_index' => 1 // University subscription
            ],

            // Civil Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte civile - 2025',
                'nom_en' => 'Civil Card Fee - 2025',
                'nom_ar' => 'رسوم البطاقة المدنية - 2025',
                'amount' => 3.00,
                'date_start' => now()->startOfYear(),
                'date_end' => now()->endOfYear(),
                'subs_type_index' => 2 // Civil subscription
            ],

            // Impersonal Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte impersonnelle - 2025',
                'nom_en' => 'Impersonal Card Fee - 2025',
                'nom_ar' => 'رسوم البطاقة غير الشخصية - 2025',
                'amount' => 3.50,
                'date_start' => now()->startOfYear(),
                'date_end' => now()->endOfYear(),
                'subs_type_index' => 3 // Impersonal subscription
            ],
    
            // Contracted Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte conventionnée - 2025',
                'nom_en' => 'Contracted Card Fee - 2025',
                'nom_ar' => 'رسوم البطاقة التعاقدية - 2025',
                'amount' => 4.00,
                'date_start' => now()->startOfYear(),
                'date_end' => now()->endOfYear(),
                'subs_type_index' => 4 // Contracted subscription
            ],
        ];

        foreach ($cardFees as $cardFee) {
            if (isset($subsTypes[$cardFee['subs_type_index']])) {
                CardFee::create([
                    'nom_fr' => $cardFee['nom_fr'],
                    'nom_en' => $cardFee['nom_en'],
                    'nom_ar' => $cardFee['nom_ar'],
                    'amount' => $cardFee['amount'],
                    'id_subs_type' => $subsTypes[$cardFee['subs_type_index']]->id,
                    'date_start' => $cardFee['date_start'],
                    'date_end' => $cardFee['date_end']
                ]);
            }
        }
    }
}

