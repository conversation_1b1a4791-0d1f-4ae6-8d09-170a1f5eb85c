<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AffectationCardTypeResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'start_serial_number' => $this->start_serial_number,
            'end_serial_number' => $this->end_serial_number,
            'id_card_type' => $this->id_card_type,
            'id_agent' => $this->id_agent,
            'agent' => new AdminResource($this->whenLoaded('agent')),
            'card_types' => AffectationCardTypeResource::collection($this->whenLoaded('ranges')),
            'card_type' => new CardTypeResource($this->whenLoaded('cardType')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}