<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLineRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'CODE_LINE' => 'required|string|max:255|unique:lines,CODE_LINE',
            'type_service' => 'required|in:normal,confort',
            'status' => 'required|boolean',
            'commercial_speed' => 'required|numeric|min:0|max:999999.99'
        ];
    }
}