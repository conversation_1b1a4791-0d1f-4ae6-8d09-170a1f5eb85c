<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOptionRequest;
use App\Http\Requests\UpdateOptionRequest;
use App\Http\Resources\OptionResource;
use App\Models\Option;
use App\Repositories\OptionRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class OptionController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(OptionRepository::class);
        $this->authorizeResource(Option::class, 'option');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $options = $this->repository
            ->with(['seasons'])
            ->latest()
            ->paginate();
        return OptionResource::collection($options);
    }

    public function all(): AnonymousResourceCollection
    {
        $options = $this->repository->all();
        return OptionResource::collection($options);
    }

    
    public function store(Request $request): JsonResponse
    {
        $option = Option::create([
            'nom_fr' => $request->nom_fr,
            'nom_en' => $request->nom_en,
            'nom_ar' => $request->nom_ar,
        ]);

        if ($request->has('seasons')) {
            $option->seasons()->attach($request->seasons);
        }

        return response()->json([
            'message' => 'Option created successfully',
            'data' => $option->load('seasons')
        ], 201);
    }

    public function show(Option $option): OptionResource
    {
        return new OptionResource($option);
    }

    public function update(Request $request, Option $option): JsonResponse
    {
        $option->update([
            'nom_fr' => $request->nom_fr,
            'nom_en' => $request->nom_en,
            'nom_ar' => $request->nom_ar,
        ]);

        if ($request->has('seasons')) {
            $option->seasons()->sync($request->seasons);
        }

        return response()->json([
            'message' => 'Option updated successfully',
            'data' => $option->load('seasons')
        ]);
    }

    public function destroy(Option $option): JsonResponse
    {
        try {
            $this->repository->delete($option->id);
            return response()->json([
                'message' => 'Option deleted successfully'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Option cannot be deleted, it is used in other records'
            ], 422);
        }
    }
}
