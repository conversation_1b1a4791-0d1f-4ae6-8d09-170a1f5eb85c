<?php

namespace App\Repositories;

use App\Models\TariffOption;
use Prettus\Repository\Eloquent\BaseRepository;

class TariffOptionRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_trip' => '=',
        'id_subs_type' => '=',
        'is_regular' => '=',
        'id_tariff_base' => '=',
        'manual_tariff' => '='
    ];

    public function model(): string
    {
        return TariffOption::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}