<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\MotifDuplicate;
use Illuminate\Auth\Access\HandlesAuthorization;

class MotifDuplicatePolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_duplicate_motifs');
    }

    public function view(Admin $admin, MotifDuplicate $motifDuplicate): bool
    {
        return $admin->hasPermissionTo('view_duplicate_motifs');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_duplicate_motifs');
    }

    public function update(Admin $admin, MotifDuplicate $motifDuplicate): bool
    {
        return $admin->hasPermissionTo('edit_duplicate_motifs');
    }

    public function delete(Admin $admin, MotifDuplicate $motifDuplicate): bool
    {
        return $admin->hasPermissionTo('delete_duplicate_motifs');
    }
}