<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\CardFee;

class CardFeePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_cards_fees');
    }

    public function view(Admin $admin, CardFee $cardFee): bool
    {
        return $admin->can('view_cards_fees');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_cards_fees');
    }

    public function update(Admin $admin, CardFee $cardFee): bool
    {
        return $admin->can('edit_cards_fees');
    }

    public function delete(Admin $admin, CardFee $cardFee): bool
    {
        return $admin->can('delete_cards_fees');
    }
}