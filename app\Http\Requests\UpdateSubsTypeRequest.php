<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubsTypeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'color' => 'required|string|max:7', // Assuming hex color code like #FFFFFF
            'is_student' => 'required|boolean',
            'hasCIN' => 'required|boolean',
            'is_impersonal' => 'required|boolean'
        ];
    }
}
