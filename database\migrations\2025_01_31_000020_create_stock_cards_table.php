<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('stock_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_card_type')->constrained('card_types');
            $table->foreignId('id_agent')->constrained('admins')->nullable();
            $table->integer('sequence_start');
            $table->integer('sequence_end');
            $table->enum('mouvement', ['retour', 'ajout'])->default('ajout');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stock_cards');
    }
};
