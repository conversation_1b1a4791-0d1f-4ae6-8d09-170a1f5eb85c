<?php

namespace Database\Seeders;

use App\Models\Agency;
use App\Models\Delegation;
use Illuminate\Database\Seeder;

class AgencySeeder extends Seeder
{
    public function run(): void
    {
        $agencies = [
            [
                'nom_fr' => 'Agence SNTRI Tunis',
                'nom_en' => 'SNTRI Tunis Agency',
                'nom_ar' => 'وكالة الشركة الوطنية للنقل بين المدن تونس',
                'code' => 'SNTRI-TUN',
                'contact' => '71333777',
                'address' => 'Rue Bab Alioua, 1006 Tunis',
                'delegation_name' => 'Bab El Bhar'
            ],
            [
                'nom_fr' => 'Agence SNTRI Sfax',
                'nom_en' => 'SNTRI Sfax Agency',
                'nom_ar' => 'وكالة الشركة الوطنية للنقل بين المدن صفاقس',
                'code' => 'SNTRI-SFX',
                'contact' => '71333777',
                'address' => 'Avenue Majida Boulila, 3000 Sfax',
                'delegation_name' => 'Sfax Ville'
            ],
            [
                'nom_fr' => 'Agence SNTRI Sousse',
                'nom_en' => 'SNTRI Sousse Agency',
                'nom_ar' => 'وكالة الشركة الوطنية للنقل بين المدن سوسة',
                'code' => 'SNTRI-SOU',
                'contact' => '71333777',
                'address' => 'Avenue Mohamed V, 4000 Sousse',
                'delegation_name' => 'Sousse Ville'
            ],
            [
                'nom_fr' => 'Agence TUT Tunis Nord',
                'nom_en' => 'TUT North Tunis Agency',
                'nom_ar' => 'وكالة شركة نقل تونس تونس الشمالية',
                'code' => 'TUT-TUN-N',
                'contact' => '71333777',
                'address' => 'Avenue Habib Bourguiba, 1001 Tunis',
                'delegation_name' => 'Bab El Bhar'
            ],
            [
                'nom_fr' => 'Agence TUT Tunis Sud',
                'nom_en' => 'TUT South Tunis Agency',
                'nom_ar' => 'وكالة شركة نقل تونس تونس الجنوبية',
                'code' => 'TUT-TUN-S',
                'contact' => '71333777',
                'address' => 'Rue de Rome, 1002 Tunis',
                'delegation_name' => 'Bab Bhar'
            ],
            [
                'nom_fr' => 'Agence STS Sfax Centre',
                'nom_en' => 'STS Sfax Center Agency',
                'nom_ar' => 'وكالة شركة النقل بصفاقس المركز',
                'code' => 'STS-SFX-C',
                'contact' => '71333777',
                'address' => 'Rue Mongi Slim, 3000 Sfax',
                'delegation_name' => 'Sfax Ville'
            ],
            [
                'nom_fr' => 'Agence SRS Sousse Centre',
                'nom_en' => 'SRS Sousse Center Agency',
                'nom_ar' => 'وكالة شركة النقل بسوسة المركز',
                'code' => 'SRS-SOU-C',
                'contact' => '71333777',
                'address' => 'Avenue du 14 Janvier, 4000 Sousse',
                'delegation_name' => 'Sousse Ville'
            ],
            [
                'nom_fr' => 'Agence SRTG Gabès',
                'nom_en' => 'SRTG Gabes Agency',
                'nom_ar' => 'وكالة الشركة الجهوية للنقل بقابس',
                'code' => 'SRTG-GAB',
                'contact' => '71333777',
                'address' => 'Avenue Farhat Hached, 6000 Gabès',
                'delegation_name' => 'Gabes Medina'
            ],
            [
                'nom_fr' => 'Agence SRTK Kairouan',
                'nom_en' => 'SRTK Kairouan Agency',
                'nom_ar' => 'وكالة الشركة الجهوية للنقل بالقيروان',
                'code' => 'SRTK-KAI',
                'contact' => '71333777',
                'address' => 'Avenue de la République, 3100 Kairouan',
                'delegation_name' => 'Kairouan Nord'
            ],
            [
                'nom_fr' => 'Agence SRTGN Gafsa Nord',
                'nom_en' => 'SRTGN North Gafsa Agency',
                'nom_ar' => 'وكالة الشركة الجهوية للنقل بقفصة الشمالية',
                'code' => 'SRTGN-GAF',
                'contact' => '71333777',
                'address' => 'Rue de l\'Indépendance, 2100 Gafsa',
                'delegation_name' => 'Gafsa Nord'
            ]
        ];

        foreach ($agencies as $agencyData) {
            $delegation = Delegation::where('nom_fr', $agencyData['delegation_name'])->first();
            
            if ($delegation) {
                Agency::create([
                    'nom_fr' => $agencyData['nom_fr'],
                    'nom_en' => $agencyData['nom_en'],
                    'nom_ar' => $agencyData['nom_ar'],
                    'code' => $agencyData['code'],
                    'contact' => $agencyData['contact'],
                    'address' => $agencyData['address'],
                    'id_delegation' => $delegation->id,
                    'id_governorate' => $delegation->id_governorate
                ]);
            }
        }
    }
}