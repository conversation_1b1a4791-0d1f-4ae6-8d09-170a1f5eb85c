<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affectation_card_types', function (Blueprint $table) {
            $table->id();
            $table->integer("start_serial_number");
            $table->integer("end_serial_number");
            $table->bigInteger('current_serial_number')->nullable();
            $table->foreignId('id_card_type')->nullable()->constrained('card_types');
            $table->foreignId('id_agent')->nullable()->constrained('admins');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affectation_card_types');
    }
};
