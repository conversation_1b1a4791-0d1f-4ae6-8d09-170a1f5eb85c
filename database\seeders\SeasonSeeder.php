<?php

namespace Database\Seeders;

use App\Models\Season;
use Illuminate\Database\Seeder;

class SeasonSeeder extends Seeder
{
    public function run(): void
    {
        Season::create([
            'nom_fr' => 'Période normale',
            'nom_en' => 'Normal Period',
            'nom_ar' => 'الأيام العادية',
            'start_date' => '2024-12-21',
            'end_date' => '2025-03-20',
            'priority' => 1
        ]);

        Season::create([
            'nom_fr' => 'Été',
            'nom_en' => 'Summer',
            'nom_ar' => 'صيف',
            'start_date' => '2024-06-21',
            'end_date' => '2024-09-22',
            'priority' => 2
        ]);

        Season::create([
            'nom_fr' => 'Ramadhan',
            'nom_en' => 'Ramadhan',
            'nom_ar' => 'رمضان',
            'start_date' => '2024-09-23',
            'end_date' => '2024-12-20',
            'priority' => 3
        ]); 
    }
}

