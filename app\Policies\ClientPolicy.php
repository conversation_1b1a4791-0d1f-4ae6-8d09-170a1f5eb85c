<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Client;

class ClientPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_clients');
    }

    public function view(Admin $admin, Client $client): bool
    {
        return $admin->hasPermissionTo('view_clients');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_clients');
    }

    public function update(Admin $admin, Client $client): bool
    {
        return $admin->hasPermissionTo('edit_clients');
    }

    public function delete(Admin $admin, Client $client): bool
    {
        return $admin->hasPermissionTo('delete_clients');
    }
}