<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Agency;

class AgencyPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view_agencies');
    }

    public function view(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('view_agencies');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_agencies');
    }

    public function update(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('edit_agencies');
    }

    public function delete(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('delete_agencies');
    }
}
