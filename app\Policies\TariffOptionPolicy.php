<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TariffOption;

class TariffOptionPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return true;
    }

    public function view(Admin $admin, TariffOption $tariffOption): bool
    {
        return true;
    }

    public function create(Admin $admin): bool
    {
        return true;
    }

    public function update(Admin $admin, TariffOption $tariffOption): bool
    {
        return true;
    }

    public function delete(Admin $admin, TariffOption $tariffOption): bool
    {
        return true;
    }
}