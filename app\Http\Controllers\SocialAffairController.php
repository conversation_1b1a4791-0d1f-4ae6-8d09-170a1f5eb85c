<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportSocialAffairRequest;
use App\Http\Requests\StoreSocialAffairRequest;
use App\Http\Requests\UpdateSocialAffairRequest;
use App\Http\Resources\SocialAffairResource;
use App\Imports\SocialAffairsImport;
use App\Models\SocialAffair;
use App\Repositories\SocialAffairRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Maatwebsite\Excel\Facades\Excel;

class SocialAffairController extends Controller
{
    private SocialAffairRepository $repository;

    public function __construct(SocialAffairRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SocialAffair::class, 'is_social_affair');
    }

    /**
     * Register methods that should be authorized.
     *
     * @return array
     */
    protected function resourceAbilityMap()
    {
        return array_merge(
            parent::resourceAbilityMap(),
            [
                'import' => 'import',
                'all' => 'viewAny',
                'getByGovernorate' => 'viewAny',
                'verify' => 'verify'
            ]
        );
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        return SocialAffairResource::collection(
            $this->repository
                ->with(['governorate', 'academicYear'])
                ->orderBy('created_at', 'desc')
                ->paginate($request->input('perPage'))
        );
    }

    /**
     * Get all social affairs without pagination.
     */
    public function all(): AnonymousResourceCollection
    {
        return SocialAffairResource::collection(
            $this->repository->with(['governorate', 'academicYear'])->all()
        );
    }

    /**
     * Get social affairs by governorate.
     */
    public function getByGovernorate($governorate): AnonymousResourceCollection
    {
        return SocialAffairResource::collection(
            $this->repository
                ->scopeQuery(function($query) use ($governorate) {
                    return $query->where('governorate_id', $governorate)
                                 ->orderBy('created_at', 'desc');
                })
                ->with(['governorate', 'academicYear'])
                ->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSocialAffairRequest $request): JsonResponse
    {
        $socialAffair = $this->repository->create($request->validated());
        $socialAffair->load(['governorate', 'academicYear']);

        return response()->json([
            'message' => 'Social affair created successfully',
            'data' => new SocialAffairResource($socialAffair)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SocialAffair $socialAffair): SocialAffairResource
    {
        $socialAffair->load(['governorate', 'academicYear']);
        return new SocialAffairResource($socialAffair);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSocialAffairRequest $request, SocialAffair $socialAffair): JsonResponse
    {
        $socialAffair = $this->repository->update($request->validated(), $socialAffair->id);
        $socialAffair->load(['governorate', 'academicYear']);

        return response()->json([
            'message' => 'Social affair updated successfully',
            'data' => new SocialAffairResource($socialAffair)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SocialAffair $socialAffair): JsonResponse
    {
        $this->repository->delete($socialAffair->id);

        return response()->json([
            'message' => 'Social affair deleted successfully'
        ]);
    }

    /**
     * Import social affairs from CSV/Excel file.
     */
    public function import(ImportSocialAffairRequest $request): JsonResponse
    {
        $this->authorize('import', SocialAffair::class);
        $file = $request->file('file');
        $governorate_id = $request->input('governorate_id');
        $academic_year_id = $request->input('academic_year_id');

        try {
            Excel::import(new SocialAffairsImport($governorate_id, $academic_year_id), $file);

            return response()->json([
                'message' => 'Social affairs imported successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error importing social affairs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify social affair.
     */
    public function verify(Request $request): JsonResponse
    {
        $this->authorize('verify', SocialAffair::class);
        $dob = $request->input('dob');
        $identifier = $request->input('identifier');
        $eleve_etudiant = $request->input('eleve_etudiant');

        if (!$dob || !$identifier || !$eleve_etudiant) {
            return response()->json([
                'status' => 'error',
                'message' => 'dob, identifier, and eleve_etudiant are required',
                'exists' => false,
                'has_purchase_order' => false
            ], 400);
        }
        $socialAffair = $this->repository->with('governorate')->findWhere([
            'eleve_etudiant' => $eleve_etudiant,
            'identifier' => $identifier,
            'dob' => $dob
        ])->first();
        $exists = $socialAffair !== null;

        if (!$exists) {
            return response()->json([
                'status' => 'not_found',
                'exists' => false,
                'has_purchase_order' => false
            ]);
        }

        $governorateId = $socialAffair->governorate_id;
        $purchaseOrderRepository = resolve(\App\Repositories\GovernoratePurchaseOrderRepository::class);

        $purchaseOrders = $purchaseOrderRepository->findWhere([
            'id_governorate' => $governorateId,
            'status' => true
        ]);

        $hasAvailableAmount = $purchaseOrders->sum('current_amount') > 0;

        if (!$hasAvailableAmount) {
            return response()->json([
                'status' => 'no_purchase_order',
                'exists' => true,
                'has_purchase_order' => false,
                'user_data' => [
                    'governorate_id' => $governorateId,
                    'governorate_name' => $socialAffair->governorate->nom_fr,
                    'nom_complet' => $socialAffair->nom_complet,
                    'eleve_etudiant' => $socialAffair->eleve_etudiant
                ]
            ]);
        }

        return response()->json([
            'status' => 'success',
            'exists' => true,
            'has_purchase_order' => true,
            'user_data' => [
                'governorate_id' => $governorateId,
                'governorate_name' => $socialAffair->governorate->nom_fr,
                'nom_complet' => $socialAffair->nom_complet,
                'eleve_etudiant' => $socialAffair->eleve_etudiant
            ],
            'purchase_orders' => [
                'total_amount' => $purchaseOrders->sum('current_amount'),
                'count' => $purchaseOrders->count()
            ]
        ]);
    }


    /**
     * Verify social affair with cin parent.
     */
    public function verifyCinParent(Request $request): JsonResponse
    {
        $this->authorize('verify', SocialAffair::class);

        $cin_parent = $request->input('cin_parent');
        $final_amount = $request->input('final_amount');

        if (!$cin_parent) {
            return response()->json([
                'status' => 'error',
                'message' => 'cin parent are required',
                'exists' => false,
                'has_purchase_order' => false
            ], 400);
        }
        $socialAffair = $this->repository->with('governorate')->findWhere([
            'cin_parent' => $cin_parent,
        ])->first();
        $exists = $socialAffair !== null;

        if (!$exists) {
            return response()->json([
                'social_affair' => $socialAffair,
                'status' => 'not_found',
                'exists' => false,
                'has_purchase_order' => false
            ]);
        }

        $hasAvailableAmount = $socialAffair->governorate->purchase_amount > $final_amount;

        if (!$hasAvailableAmount) {
            return response()->json([
                'status' => 'no_purchase_order',
                'exists' => true,
                'has_purchase_order' => false,
                'governorate_data' => $socialAffair->governorate,
            ]);
        }

        return response()->json([
            'status' => 'success',
            'exists' => true,
            'has_purchase_order' => true,
            'governorate_data' => $socialAffair->governorate,
        ]);
    }
}
