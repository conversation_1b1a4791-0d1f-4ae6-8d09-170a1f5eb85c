<?php

namespace App\Repositories;

use App\Models\Trip;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TripRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'id_line' => '=',
        'id_station_start' => '=',
        'id_station_end' => '=',
        'status' => '=',
        'inter_station' => '=',
        'is_aller_retour' => '=',
        'number_of_km' => '='
    ];

    public function model(): string
    {
        return Trip::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    /**
     * Apply custom search criteria for relationship fields
     */
    public function applyCustomSearch($query, $search, $searchJoin = 'and')
    {
        if (empty($search)) {
            return $query;
        }

        $searchFields = [];

        // Parse search string (format: field1:value1;field2:value2)
        $searchPairs = explode(';', $search);
        foreach ($searchPairs as $pair) {
            if (strpos($pair, ':') !== false) {
                [$field, $value] = explode(':', $pair, 2);
                $searchFields[trim($field)] = trim($value);
            }
        }

        foreach ($searchFields as $field => $value) {
            if ($field === 'id_subs_type') {
                // Handle subscription type search through relationship
                $query = $query->whereHas('tariffOptions', function($q) use ($value) {
                    $q->where('id_subs_type', $value);
                });
            } else {
                // Handle regular fields
                if (isset($this->fieldSearchable[$field])) {
                    $operator = $this->fieldSearchable[$field];
                    if ($operator === 'like') {
                        $query = $query->where($field, 'like', "%{$value}%");
                    } else {
                        $query = $query->where($field, $operator, $value);
                    }
                }
            }
        }

        return $query;
    }

    public function create(array $data): Trip
    {
        try {
            DB::beginTransaction();

            // Prepare trip data
            $tripData = [
                'nom_fr' => $data['nom_fr'],
                'nom_en' => $data['nom_en'],
                'nom_ar' => $data['nom_ar'],
                'id_line' => $data['id_line'],
                'id_station_start' => $data['stations']['id_station_start'],
                'id_station_end' => $data['stations']['id_station_end'],
                'status' => $data['status'],
                'inter_station' => $data['inter_station'],
                'is_aller_retour' => $data['is_aller_retour'],
                'number_of_km' => $data['number_of_km']
            ];

            // Create trip
            $trip = parent::create($tripData);

            // Create tariff options
            if (!empty($data['tariff_options'])) {
                foreach ($data['tariff_options'] as $option) {
                    $tariffData = [
                        'id_trip' => $trip->id,
                        'id_subs_type' => $option['id_subs_type'],
                        'is_regular' => $option['is_regular'],
                        'id_tariff_base' => $option['id_tariff_base'] ?? null,
                        'manual_tariff' => $option['manual_tariff'] ?? null
                    ];

                    $trip->tariffOptions()->create($tariffData);
                }
            }

            DB::commit();

            return $trip->load(['line', 'startStation', 'endStation', 'tariffOptions']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, $id): Trip
    {
        try {
            DB::beginTransaction();

            $trip = $this->find($id);

            $tripData = array_filter([
                'nom_fr' => $data['nom_fr'] ?? null,
                'nom_en' => $data['nom_en'] ?? null,
                'nom_ar' => $data['nom_ar'] ?? null,
                'id_line' => $data['id_line'] ?? null,
                'status' => $data['status'] ?? null,
                'inter_station' => $data['inter_station'] ?? null,
                'is_aller_retour' => $data['is_aller_retour'] ?? null,
                'number_of_km' => $data['number_of_km'] ?? null,
                'id_station_start' => $data['stations']['id_station_start'] ?? null,
                'id_station_end' => $data['stations']['id_station_end'] ?? null,
            ], function($value) {
                return !is_null($value);
            });

            $trip = parent::update($tripData, $id);

            if (isset($data['tariff_options'])) {
                $trip->tariffOptions()->delete();

                foreach ($data['tariff_options'] as $option) {
                    $tariffData = [
                        'id_trip' => $trip->id,
                        'id_subs_type' => $option['id_subs_type'],
                        'is_regular' => $option['is_regular'],
                        'id_tariff_base' => $option['id_tariff_base'] ?? null,
                        'manual_tariff' => $option['manual_tariff'] ?? null
                    ];

                    $trip->tariffOptions()->create($tariffData);
                }
            }

            DB::commit();
            return $trip->fresh()->load(['line', 'startStation', 'endStation', 'tariffOptions']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete($id): bool
    {
        try {
            DB::beginTransaction();

            $trip = $this->find($id);

            // Delete related tariff options first
            $trip->tariffOptions()->delete();

            // Delete the trip
            $result = parent::delete($id);

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Filter trips by subscription type ID
     *
     * @param int $subsTypeId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function filterBySubsType(int $subsTypeId)
    {
        return $this->model->whereHas('tariffOptions', function($query) use ($subsTypeId) {
            $query->where('id_subs_type', $subsTypeId);
        });
    }

    /**
     * Get trips by subscription type ID with relationships
     *
     * @param int $subsTypeId
     * @param array $with
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBySubsType(int $subsTypeId, array $with = ['line', 'startStation', 'endStation', 'tariffOptions'])
    {
        return $this->filterBySubsType($subsTypeId)
                    ->with($with)
                    ->get();
    }

    /**
     * Get paginated trips by subscription type ID
     *
     * @param int $subsTypeId
     * @param int $perPage
     * @param array $with
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginateBySubsType(int $subsTypeId, int $perPage = 15, array $with = ['line', 'startStation', 'endStation', 'tariffOptions'])
    {
        return $this->filterBySubsType($subsTypeId)
                    ->with($with)
                    ->paginate($perPage);
    }

    /**
     * Get non-inter-station trips by subscription type
     *
     * @param int $subsTypeId
     * @param array $with
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getNonInterStationBySubsType(int $subsTypeId, array $with = ['line', 'startStation', 'endStation', 'tariffOptions'])
    {
        return $this->filterBySubsType($subsTypeId)
                    ->where('inter_station', false)
                    ->with($with)
                    ->get();
    }

    /**
     * Get paginated non-inter-station trips by subscription type
     *
     * @param int $subsTypeId
     * @param int $perPage
     * @param array $with
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginateNonInterStationBySubsType(int $subsTypeId, int $perPage = 15, array $with = ['line', 'startStation', 'endStation', 'tariffOptions'])
    {
        return $this->filterBySubsType($subsTypeId)
                    ->where('inter_station', false)
                    ->with($with)
                    ->latest()
                    ->paginate($perPage);
    }

    /**
     * Apply subscription type filter to any query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $subsTypeId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function applySubsTypeFilter($query, int $subsTypeId)
    {
        return $query->whereHas('tariffOptions', function($q) use ($subsTypeId) {
            $q->where('id_subs_type', $subsTypeId);
        });
    }
}



