<?php

namespace App\Http\Controllers;

use App\Http\Requests\forgetPasswordRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\ResetPasswordChange;
use App\Http\Requests\ResetPasswordRequest;
use App\Jobs\SendResetPasswordEmail;
use App\Mail\ResetPasswordMail;
use App\Models\PasswordResetToken;
use App\Repositories\AdminRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Tymon\JWTAuth\Exceptions\JWTException;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{

    protected $repository;

    public function __construct(AdminRepository $repository)
    {
        $this->repository = $repository;
    }

    /*|--------------------------------------------------------------------------
    |  Login user
    |-------------------------------------------------------------------------- */
    public function loginAdmin(LoginRequest $request)
    {
        $credentials = ['password' => $request->password];

        if (filter_var($request->identifier, FILTER_VALIDATE_EMAIL)) {
            $credentials['email'] = $request->identifier;
        } elseif (preg_match('/^\+216-\d{8}$/', $request->identifier)) {
            $phoneNumber = preg_replace('/^\+216-/', '', $request->identifier);
            $credentials['phone'] = $phoneNumber;
        } else {
            $credentials['cin'] = $request->identifier;
        }

        $user = null;
        if (isset($credentials['email'])) {
            $user = \App\Models\Admin::where('email', $credentials['email'])->first();
        }

        if (!$user) {
            return response()->json(['error' => 'Identifiants incorrects'], 401);
        }

        try {
            if (!$token = Auth::guard('api')->attempt($credentials)) {
                return response()->json(['error' => 'Identifiants incorrects'], 401);
            }
        } catch (JWTException $e) {
            return response()->json(['error' => 'Impossible de créer le token'], 500);
        }

        $admin = Auth::guard('api')->user();

        $roles = $admin->getRoleNames();
        $permissions = $admin->getAllPermissions()->pluck('name');

        $customClaims = [
            'user' => Auth::guard('api')->user()->withoutRelations()
        ];

        $token = Auth::guard('api')->claims($customClaims)->attempt($credentials);
        return response()->json([
            'status' => 'success',
            'message' => 'Authentification réussie',
            'token' => $token,
            'permissions' => $permissions,
            'roles' => $roles,
        ]);
    }

    /*|--------------------------------------------------------------------------
    | Refresh JWT token
    |-------------------------------------------------------------------------- */
    public function refreshToken()
    {
        $newToken = auth()->refresh();
        return response()->json([
            "status" => "success",
            "message" => "access token refreshed",
            "token" => $newToken
        ]);
    }

    /*|--------------------------------------------------------------------------
    | Logout user
    |-------------------------------------------------------------------------- */
    public function logoutAdmin(Request $request)
    {
        try {
            if (Auth::check()) {
                auth()->logout();
                return response()->json([
                    "status" => "success",
                    "message" => "Logged out successfully"
                ]);
            } else {
                return response()->json([
                    "status" => "error",
                    "message" => "No active session found"
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                "status" => "error",
                "message" => "Failed to log out",
                "error" => $e->getMessage()
            ], 500);
        }
    }

    public function verifyToken(Request $request)
    {
        try {
            return response()->json([
                'status' => 'success',
                'message' => 'verification success',
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Token is invalid or expired'], Response::HTTP_UNAUTHORIZED);
        }
    }

    public function forgetPassword(forgetPasswordRequest $request)
    {
        $user = $this->repository->findWhere(['email' => $request->email])->first();
        $resetPasswordToken = str_pad(random_int(10000000, 99999999), 8, '0', STR_PAD_LEFT);
        PasswordResetToken::updateOrCreate(
            ['email' => $user->email],
            [
                'token' => $resetPasswordToken,
            ]
        );
        $data = [$user->lastname . ' ' . $user->firstname, $resetPasswordToken];
        //TODO : send mail en background
        SendResetPasswordEmail::dispatch($request->email, $data);
        return response()->json([
            'status' => 'success',
            'message' => 'verification success',
            'token' => $resetPasswordToken,
        ], Response::HTTP_OK);
    }
    public function verifcoderesetPassword(ResetPasswordRequest $request)
    {
        $user = $this->repository->findWhere(['email' => $request->email])->first();
        if (!$user) {
            return response()->json([
                "status" => "error",
                "message" => "Utilisateur n'existe pas",
            ], 404);
        }
        $resetRequest = PasswordResetToken::where('email', $user->email)->first();
        $createdAt = \Carbon\Carbon::parse($resetRequest->created_at);
        if (!$resetRequest || $resetRequest->token !== $request->token) {
            return response()->json([
                'status' => 'error',
                'message' => 'Le token est invalide.',
            ], 404);
        }
        if (now()->diffInMinutes($createdAt) > 7200) {
            return response()->json([
                'status' => 'error',
                'message' => 'Le token est expiré.',
            ], 404);
        }
        PasswordResetToken::where('email', $user->email)->delete();
        return response()->json([
            'status' => 'success',
            'message' => 'Token valide',
        ], Response::HTTP_OK);
    }
    public function resetPassword(ResetPasswordChange $request)
    {
        $user = $this->repository->findWhere(['email' => $request->email])->first();
        $this->repository->update([
            'password' => Hash::make($request->password)
        ], $user->id);
        return response()->json([
            'status' => 'success',
            'message' => 'Token valide',
        ], Response::HTTP_OK);
    }
}
