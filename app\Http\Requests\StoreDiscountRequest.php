<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDiscountRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'percentage' => 'required|numeric|min:0|max:100',
            'is_stagiaire' => 'required|boolean',
            'special_client' => 'nullable|in:SCOLAIRE,UNIVERSITAIRE',
            'date_start' => 'required|date',
            'date_end' => 'required|date|after_or_equal:date_start',
            'id_periodicities' => 'required|array',
            'id_periodicities.*' => 'required|integer|exists:periodicities,id',
            'id_subs_type' => 'required|integer|exists:subs_types,id'
        ];
    }
}

