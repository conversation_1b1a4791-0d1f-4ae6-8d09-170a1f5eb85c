<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SalePoint;
use Illuminate\Auth\Access\HandlesAuthorization;

class SalePointPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_sales_points');
    }

    public function view(Admin $admin, SalePoint $salePoint): bool
    {
        return $admin->can('view_sales_points');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_sales_points');
    }

    public function update(Admin $admin, SalePoint $salePoint): bool
    {
        return $admin->can('edit_sales_points');
    }

    public function delete(Admin $admin, SalePoint $salePoint): bool
    {
        return $admin->can('delete_sales_points');
    }
}