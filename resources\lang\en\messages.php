<?php

return [
    'card_type' => [
        'delete' => [
            'error' => 'Cannot delete card type that is being used in affectations or subscriptions',
            'success' => 'Card type deleted successfully'
        ]
    ],
    'client' => [
        'has_cin' => [
            'not_found' => 'No clients found',
            'found' => 'Clients retrieved successfully'
        ]
    ],
    'subscription' => [
        'affectation_not_found' => 'Affectation not found',
        'duplicated_subscription' => 'This client already has a school or university subscription in the current sales period.',
        'invalid_trip_tariff_options' => 'The selected trip does not have tariff options for this subscription type.'
    ]
];

