<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWebsiteTripRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'id_line' => 'required|exists:lines,id',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id',
            'status' => 'boolean',
            'number_of_km' => 'required|numeric|min:0',
        ];
    }
}
