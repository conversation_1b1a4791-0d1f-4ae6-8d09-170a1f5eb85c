<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AffectationAgentResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'id_agent' => $this->id_agent,
            'id_sale_point' => $this->id_sale_point,
            'id_sale_period' => $this->id_sale_period,
            'date_start' => $this->date_start,
            'date_end' => $this->date_end,
            'agent' => new AdminResource($this->whenLoaded('agent')),
            'sale_point' => new SalePointResource($this->whenLoaded('salePoint')),
            'sale_period' => new SalePeriodResource($this->whenLoaded('salePeriod')),
            'payment_methods' => PaymentMethodResource::collection($this->whenLoaded('paymentMethods')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
