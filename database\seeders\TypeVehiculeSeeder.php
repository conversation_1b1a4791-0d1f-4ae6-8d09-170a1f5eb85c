<?php

namespace Database\Seeders;

use App\Models\TypeVehicule;
use Illuminate\Database\Seeder;

class TypeVehiculeSeeder extends Seeder
{
    public function run(): void
    {
        $typeVehicules = [
            [
                'nom_fr' => 'Bus',
                'nom_en' => 'Bus',
                'nom_ar' => 'حافلة',
                'code' => 'BUS',
                'nbre_max_place' => 50,
                'swf' => 'bus.swf',
                'photo' => 'bus.jpg',
                'status' => true
            ],
            [
                'nom_fr' => 'Minibus',
                'nom_en' => 'Minibus',
                'nom_ar' => 'حافلة صغيرة',
                'code' => 'MINIBUS',
                'nbre_max_place' => 25,
                'swf' => 'minibus.swf',
                'photo' => 'minibus.jpg',
                'status' => true
            ],
            [
                'nom_fr' => 'Voiture',
                'nom_en' => 'Car',
                'nom_ar' => 'سيارة',
                'code' => 'CAR',
                'nbre_max_place' => 5,
                'swf' => 'car.swf',
                'photo' => 'car.jpg',
                'status' => true
            ]
        ];

        foreach ($typeVehicules as $typeVehicule) {
            TypeVehicule::create($typeVehicule);
        }
    }
}
