<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCampaignRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'status' => 'sometimes|boolean',
            'id_abn_type' => 'sometimes|required|exists:subs_types,id',
        ];
    }
}
