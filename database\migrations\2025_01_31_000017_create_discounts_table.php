<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('percentage', 5, 2);
            $table->boolean('is_stagiaire')->default(false);
            $table->enum('special_client', ['SCOLAIRE', 'UNIVERSITAIRE'])->nullable();
            $table->date('date_start');
            $table->date('date_end');
            $table->foreignId('id_subs_type')->constrained('subs_types');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('discounts');
    }
};


