<?php

namespace Database\Seeders;

use App\Models\MotifDuplicate;
use Illuminate\Database\Seeder;

class MotifDuplicaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {  
        $motifs = [
            // Duplicat 
            [
                'nom_fr' => 'Perte',
                'nom_en' => 'Loss',
                'nom_ar' => 'ضياع',
                'amount' => 10.00,
                'id_card_type' => 2
            ],
            [
                'nom_fr' => 'Vol',
                'nom_en' => 'Theft',
                'nom_ar' => 'سرقة',
                'amount' => 15.00,
                'id_card_type' => 2
            ],
            [
                'nom_fr' => 'Détruite',
                'nom_en' => 'Destroyed',
                'nom_ar' => 'تلف',
                'amount' => 20.00,
                'id_card_type' => 2
            ],
            [
                'nom_fr' => 'Informations modifiées',
                'nom_en' => 'Information modified',
                'nom_ar' => 'معلومات محدثة',
                'amount' => 25.00,
                'id_card_type' => 2
            ],
            // Reimpression
            [
                'nom_fr' => 'Reimpression',
                'nom_en' => 'Reprint',
                'nom_ar' => 'طباعة مرة أخرى',
                'amount' => 0.00,
                'id_card_type' => 1
            ],
        ];
        
        // Create motifs
        foreach ($motifs as $motif) {
            MotifDuplicate::create($motif);
        }
    }
}
