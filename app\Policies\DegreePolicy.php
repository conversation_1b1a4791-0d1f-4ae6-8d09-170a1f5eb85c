<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Degree;

class DegreePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_school_degrees');
    }

    public function view(Admin $admin, Degree $degree): bool
    {
        return $admin->hasPermissionTo('view_school_degrees');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_school_degrees');
    }

    public function update(Admin $admin, Degree $degree): bool
    {
        return $admin->hasPermissionTo('edit_school_degrees');
    }

    public function delete(Admin $admin, Degree $degree): bool
    {
        return $admin->hasPermissionTo('delete_school_degrees');
    }
}