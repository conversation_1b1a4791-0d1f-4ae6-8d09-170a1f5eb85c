<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTypeVehicleTypeLocationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_type_vehicule' => [
                'required',
                'exists:type_vehicules,id',
                Rule::unique('type_vehicle_type_locations', 'id_type_vehicule')
                    ->where('id_type_location', $this->id_type_location)
            ],
            'id_type_location' => [
                'required',
                'exists:location_types,id',
                Rule::unique('type_vehicle_type_locations', 'id_type_location')
                    ->where('id_type_vehicule', $this->id_type_vehicule)
            ],
            'km_min' => 'nullable|integer|min:0',
            'status' => 'boolean'
        ];
    }
}

