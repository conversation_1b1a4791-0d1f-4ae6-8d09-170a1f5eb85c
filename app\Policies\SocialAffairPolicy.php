<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SocialAffair;
use Illuminate\Auth\Access\HandlesAuthorization;

class SocialAffairPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view social_affairs');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Admin $admin, SocialAffair $socialAffair): bool
    {
        return $admin->hasPermissionTo('view social_affairs');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create social_affairs');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Admin $admin, SocialAffair $socialAffair): bool
    {
        return $admin->hasPermissionTo('edit social_affairs');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Admin $admin, SocialAffair $socialAffair): bool
    {
        return $admin->hasPermissionTo('delete social_affairs');
    }

    /**
     * Determine whether the user can import social affairs.
     */
    public function import(Admin $admin): bool
    {
        return $admin->hasPermissionTo('import social_affairs');
    }

    /**
     * Determine whether the user can verify social affairs.
     */
    public function verify(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view social_affairs');
    }
}
