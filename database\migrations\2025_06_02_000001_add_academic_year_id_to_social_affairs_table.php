<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('social_affairs', function (Blueprint $table) {
            $table->foreignId('academic_year_id')->nullable()->after('governorate_id')->constrained('academic_years');
        });
    }

    public function down(): void
    {
        Schema::table('social_affairs', function (Blueprint $table) {
            $table->dropForeign(['academic_year_id']);
            $table->dropColumn('academic_year_id');
        });
    }
};
