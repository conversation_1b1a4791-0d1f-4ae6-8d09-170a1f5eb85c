<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Delegation;

class DelegationPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view_delegations');
    }

    public function view(Admin $admin, Delegation $delegation): bool
    {
        return $admin->hasPermissionTo('view_delegations');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_delegations');
    }

    public function update(Admin $admin, Delegation $delegation): bool
    {
        return $admin->hasPermissionTo('edit_delegations');
    }

    public function delete(Admin $admin, Delegation $delegation): bool
    {
        return $admin->hasPermissionTo('delete_delegations');
    }
}