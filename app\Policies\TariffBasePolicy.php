<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\TariffBase;

class TariffBasePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_tariff_bases');
    }

    public function view(Admin $admin, TariffBase $tariffBase): bool
    {
        return $admin->can('view_tariff_bases');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_tariff_bases');
    }

    public function update(Admin $admin, TariffBase $tariffBase): bool
    {
        return $admin->can('edit_tariff_bases');
    }

    public function delete(Admin $admin, TariffBase $tariffBase): bool
    {
        return $admin->can('delete_tariff_bases');
    }
}