<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Trip;

class TripPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_routes');
    }

    public function view(Admin $admin, Trip $trip): bool
    {
        return $admin->can('view_routes');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_routes');
    }

    public function update(Admin $admin, Trip $trip): bool
    {
        return $admin->can('edit_routes');
    }

    public function delete(Admin $admin, Trip $trip): bool
    {
        return $admin->can('delete_routes');
    }
}