<?php

namespace Database\Seeders;

use App\Models\TypeEstablishment;
use Illuminate\Database\Seeder;

class TypeEstablishmentSeeder extends Seeder
{
    public function run(): void
    {
        $typeEstablishments = [
            [
                'nom_fr' => 'École Primaire',
                'nom_en' => 'Primary School',
                'nom_ar' => 'مدرسة ابتدائية'
            ],
            [
                'nom_fr' => 'Collège',
                'nom_en' => 'Middle School',
                'nom_ar' => 'مدرسة إعدادية'
            ],
            [
                'nom_fr' => 'Lycée',
                'nom_en' => 'High School',
                'nom_ar' => 'مدرسة ثانوية'
            ],
            [
                'nom_fr' => 'Université',
                'nom_en' => 'University',
                'nom_ar' => 'جامعة'
            ],
            [
                'nom_fr' => 'Institut Supérieur',
                'nom_en' => 'Higher Institute',
                'nom_ar' => 'معهد عالي'
            ]
        ];
        
        foreach ($typeEstablishments as $typeEstablishment) {
            TypeEstablishment::create($typeEstablishment);
        }
    }
}

