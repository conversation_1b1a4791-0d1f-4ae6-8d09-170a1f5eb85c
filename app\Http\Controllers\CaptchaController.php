<?php

namespace App\Http\Controllers;


use <PERSON><PERSON>\Captcha\CaptchaBuilder;
use Illuminate\Support\Facades\Hash;

class CaptchaController extends Controller
{
    public function generateCaptcha()
    {
        $builder = new CaptchaBuilder();
        $builder->setBackgroundColor(r: 255, g: 255, b: 255);
        $builder->setMaxAngle(1); 
        $builder->setMaxBehindLines(0);
        $builder->setMaxFrontLines(0); 
        $builder->setPhrase(implode('', array_rand(array_flip(['2', '3', '4', '6', '7', '8', '9']), 5)));
        $builder->build();

        $captcha_text = $builder->getPhrase();
        $hashed_captcha_text  = Hash::make($captcha_text);
        $imageData = base64_encode($builder->get());

        return response()->json([
            'captcha' => 'data:image/jpeg;base64,' . $imageData,
            'hashed_captcha_text' => $hashed_captcha_text,
            'captcha_text' => $captcha_text,
        ])->header('Access-Control-Allow-Origin', '*');
    }
}
