<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class SuperAdminSeeder extends Seeder
{
    public function run(): void
    {
        $superAdminRole = Role::firstOrCreate(['name' => 'SUPER_ADMIN', 'guard_name' => 'api']);

        $superAdmin = Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'firstname' => 'Super',
                'lastname' => 'Admin',
                'phone' => '12343378',
                'cin' => '12333678',
                'address' => 'Super Admin Address',
                'password' => Hash::make('admin')
            ]
        );

        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);

        $superAdmin->assignRole($superAdminRole);
    }
}


