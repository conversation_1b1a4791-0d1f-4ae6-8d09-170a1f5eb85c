<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use Illuminate\Database\Seeder;

class AcademicYearSeeder extends Seeder
{
    public function run(): void
    {
        $academicYears = [
            [
                'code' => '2023-2024',
                'start_date' => '2023-09-01',
                'end_date' => '2024-06-30'
            ],
            [
                'code' => '2024-2025',
                'start_date' => '2024-09-01',
                'end_date' => '2025-06-30'
            ],
            [
                'code' => '2025-2026',
                'start_date' => '2025-09-01',
                'end_date' => '2026-06-30'
            ],
        ];

        foreach ($academicYears as $academicYear) {
            AcademicYear::updateOrCreate(
                ['code' => $academicYear['code']],
                [
                    'start_date' => $academicYear['start_date'],
                    'end_date' => $academicYear['end_date']
                ]
            );
        }
    }
}
