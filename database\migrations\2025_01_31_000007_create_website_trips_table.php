<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('website_trips', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->foreignId('id_line')->constrained('lines');
            $table->foreignId('id_station_start')->constrained('stations');
            $table->foreignId('id_station_end')->constrained('stations');
            $table->integer('number_of_km');
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('website_trips');
    }
};