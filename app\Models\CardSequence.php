<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;


class CardSequence extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_card_type',
        'start_sequence',
        'end_sequence',
        'status',
        'id_agent',
    ];

    /**
     * Get the card type that owns the sequence.
     */
    public function cardType(): BelongsTo
    {
        return $this->belongsTo(CardType::class, 'id_card_type');
    }

    /**
     * Get the agent that owns the sequence.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'id_agent');
    }
}
