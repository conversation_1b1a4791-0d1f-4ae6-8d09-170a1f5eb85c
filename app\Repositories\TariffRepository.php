<?php

namespace App\Repositories;

use App\Models\Tariff;
use Prettus\Repository\Eloquent\BaseRepository;

class TariffRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_tariff_base' => '=',
        'tariff' => '=',
        'date_subscription' => '=',
        'date_website' => '='
    ];

    public function model(): string
    {
        return Tariff::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
