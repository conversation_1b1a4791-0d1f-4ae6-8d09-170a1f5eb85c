<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('card_sequences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_card_type');
            $table->unsignedBigInteger('start_sequence');
            $table->unsignedBigInteger('end_sequence');
            $table->enum('status', ['available', 'occupied'])->default('available');
            $table->unsignedBigInteger('id_agent')->nullable();
            $table->timestamps();

            $table->foreign('id_card_type')->references('id')->on('card_types')->onDelete('cascade');
            $table->foreign('id_agent')->references('id')->on('admins')->onDelete('set null');
            $table->index(['id_card_type', 'start_sequence', 'end_sequence']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('card_sequences');
    }
};
