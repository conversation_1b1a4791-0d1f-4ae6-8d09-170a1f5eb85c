<?php

namespace Database\Seeders;

use App\Models\Campaign;
use App\Models\SubsType;
use Illuminate\Database\Seeder;

class CampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get subscription types
        $subsTypes = SubsType::all();

        if ($subsTypes->isEmpty()) {
            $this->command->info('No subscription types found. Please run AbnTypeSeeder first.');
            return;
        }

        // Define campaign data with subscription types
        $campaigns = [
            [
                'nom_fr' => 'Campagne Rentrée Scolaire 2025',
                'nom_en' => 'Back to School Campaign 2025',
                'nom_ar' => 'حملة العودة إلى المدرسة 2025',
                'status' => true,
                'subs_type_index' => 0, // Abonnement scolaire
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Été 2025',
                'nom_en' => 'Summer Campaign 2025',
                'nom_ar' => 'حملة صيف 2025',
                'status' => true,
                'subs_type_index' => 2, // Abonnement civil
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Hiver 2025',
                'nom_en' => 'Winter Campaign 2025',
                'nom_ar' => 'حملة شتاء 2025',
                'status' => true,
                'subs_type_index' => 2, // Abonnement civil
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Spéciale Étudiants',
                'nom_en' => 'Special Student Campaign',
                'nom_ar' => 'حملة خاصة للطلاب',
                'status' => true,
                'subs_type_index' => 1, // Abonnement universitaire
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Promotionnelle',
                'nom_en' => 'Promotional Campaign',
                'nom_ar' => 'حملة ترويجية',
                'status' => false,
                'subs_type_index' => 0, // Abonnement scolaire
                'created_at' => now(),
                'updated_at' => now()
            ],
        ];

        // Insert campaigns
        foreach ($campaigns as $campaignData) {
            if (isset($subsTypes[$campaignData['subs_type_index']])) {
                $campaign = $campaignData;
                $campaign['id_abn_type'] = $subsTypes[$campaignData['subs_type_index']]->id;
                unset($campaign['subs_type_index']); // Remove the index field
                Campaign::create($campaign);
            }
        }
    }
}
