<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDiscountRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'percentage' => 'sometimes|required|numeric|min:0|max:100',
            'is_stagiaire' => 'sometimes|required|boolean',
            'special_client' => 'nullable|in:SCOLAIRE,UNIVERSITAIRE',
            'date_start' => 'sometimes|required|date',
            'date_end' => 'sometimes|required|date|after_or_equal:date_start',
            'id_periodicities' => 'sometimes|required|array',
            'id_periodicities.*' => 'required|integer|exists:periodicities,id',
            'id_subs_type' => 'sometimes|required|exists:subs_types,id'
        ];
    }
}

