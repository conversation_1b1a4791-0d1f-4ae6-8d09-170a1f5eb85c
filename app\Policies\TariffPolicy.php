<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Tariff;

class TariffPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_tariffs');
    }

    public function view(Admin $admin, Tariff $tariff): bool
    {
        return $admin->can('view_tariffs');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_tariffs');
    }

    public function update(Admin $admin, Tariff $tariff): bool
    {
        return $admin->can('edit_tariffs');
    }

    public function delete(Admin $admin, Tariff $tariff): bool
    {
        return $admin->can('delete_tariffs');
    }
}
