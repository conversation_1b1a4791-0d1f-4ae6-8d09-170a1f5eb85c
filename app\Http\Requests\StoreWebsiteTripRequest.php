<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreWebsiteTripRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'id_line' => 'required|exists:lines,id',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id|different:id_station_start',
            'status' => 'boolean',
            'number_of_km' => 'required|numeric|min:0',
        ];
    }
}