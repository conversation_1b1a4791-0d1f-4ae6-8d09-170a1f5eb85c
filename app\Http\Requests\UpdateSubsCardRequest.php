<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubsCardRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ref' => 'sometimes|string|unique:subs_cards,ref,' . $this->subs_card->id,
            'id_card_type' => 'sometimes|exists:card_types,id',
            'id_subscription' => 'sometimes|exists:subscriptions,id',
            'id_motif_duplicate' => 'sometimes|exists:motif_duplicates,id'
        ];
    }
}