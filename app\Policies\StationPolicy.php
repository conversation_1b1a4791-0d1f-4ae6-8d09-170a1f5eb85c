<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Station;

class StationPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_stations');
    }

    public function view(Admin $admin, Station $station): bool
    {
        return $admin->hasPermissionTo('view_stations');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_stations');
    }

    public function update(Admin $admin, Station $station): bool
    {
        return $admin->hasPermissionTo('edit_stations');
    }

    public function delete(Admin $admin, Station $station): bool
    {
        return $admin->hasPermissionTo('delete_stations');
    }
}