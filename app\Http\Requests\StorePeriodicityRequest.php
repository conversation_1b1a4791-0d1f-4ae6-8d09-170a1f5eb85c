<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePeriodicityRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'periodicity_code' => 'required|string|max:255|unique:periodicities',
            'max_days_per_week' => 'required|integer|min:1|max:7'
        ];
    }
}
