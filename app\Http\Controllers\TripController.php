<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTripRequest;
use App\Http\Requests\UpdateTripRequest;
use App\Http\Resources\TripResource;
use App\Models\Trip;
use App\Repositories\TripRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class TripController extends Controller
{
    private TripRepository $repository;

    public function __construct(TripRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Trip::class, 'trip');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        // Check if search contains id_subs_type (relationship field)
        $search = $request->input('search', '');
        $hasSubsTypeSearch = strpos($search, 'id_subs_type:') !== false;

        if ($hasSubsTypeSearch) {
            // Handle custom search manually to avoid RequestCriteria conflicts
            $query = Trip::with(['line', 'startStation', 'endStation', 'tariffOptions'])
                         ->where('inter_station', false);

            $query = $this->repository->applyCustomSearch(
                $query,
                $search,
                $request->input('searchJoin', 'and')
            );

            return TripResource::collection(
                $query->latest()->paginate($request->input('perPage'))
            );
        } else {
            // Use standard repository with RequestCriteria for other searches
            return TripResource::collection(
                $this->repository
                    ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
                    ->latest()->where('inter_station', false)
                    ->paginate($request->input('perPage'))
            );
        }
    }

    public function all(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->all());
    }

    public function allNotInter(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->where('inter_station', false)->get());
    }

    public function store(StoreTripRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $createdTrips = [];
            $skippedTrips = [];

            foreach ($data['line_ids'] as $lineId) {
                $existingTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $data['stations']['id_station_start'])
                    ->where('id_station_end', $data['stations']['id_station_end'])
                    ->where('inter_station', false)
                    ->first();

                if ($existingTrip) {
                    $existingSubsTypes = $existingTrip->tariffOptions()->pluck('id_subs_type')->toArray();
                    $newSubsTypes = collect($data['tariff_options'])->pluck('id_subs_type')->toArray();

                    $duplicateSubsTypes = array_intersect($existingSubsTypes, $newSubsTypes);

                    if (!empty($duplicateSubsTypes)) {
                        $skippedTrips[] = [
                            'line_id' => $lineId,
                            'reason' => 'Trip_already_exists_with_same_subscription_types',
                            'duplicate_subs_types' => $duplicateSubsTypes,
                            'existing_trip' => new TripResource($existingTrip->load(['line', 'startStation', 'endStation', 'tariffOptions']))
                        ];
                        continue;
                    }

                    foreach ($data['tariff_options'] as $option) {
                        if (!in_array($option['id_subs_type'], $existingSubsTypes)) {
                            $existingTrip->tariffOptions()->create([
                                'id_subs_type' => $option['id_subs_type'],
                                'is_regular' => $option['is_regular'],
                                'id_tariff_base' => $option['id_tariff_base'] ?? null,
                                'manual_tariff' => $option['manual_tariff'] ?? null
                            ]);
                        }
                    }

                    $createdTrips[] = new TripResource($existingTrip->fresh()->load(['line', 'startStation', 'endStation', 'tariffOptions']));
                    continue;
                }

                $tripData = $data;
                $tripData['id_line'] = $lineId;
                
                $tripData['is_aller_retour'] = $tripData['is_aller_retour'] ?? true;

                $trip = $this->repository->create($tripData);
                $createdTrips[] = new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']));
            }

            $response = [
                'created_count' => count($createdTrips),
                'skipped_count' => count($skippedTrips),
                'created_trips' => $createdTrips,
            ];

            if (!empty($skippedTrips)) {
                $response['skipped_trips'] = $skippedTrips;
            }

            if (count($createdTrips) > 0) {
                $response['message'] = count($createdTrips) === count($data['line_ids'])
                    ? 'All_trips_created_or_updated'
                    : 'Some_trips_created_others_skipped';

                return response()->json($response, 201);
            } else {
                $response['message'] = 'All_trips_already_exist_with_same_subscription_types';
                return response()->json($response, 409);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_to_create_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Trip $trip): TripResource
    {
        return new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']));
    }

    public function update(UpdateTripRequest $request, Trip $trip): JsonResponse
    {
        try {
            $data = $request->validated();

            if (isset($data['stations']) || isset($data['id_line'])) {
                $stationStart = $data['stations']['id_station_start'] ?? $trip->id_station_start;
                $stationEnd = $data['stations']['id_station_end'] ?? $trip->id_station_end;
                $lineId = $data['id_line'] ?? $trip->id_line;


                $isAllerRetour = $data['is_aller_retour'] ?? $trip->is_aller_retour;
                $existingTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationStart)
                    ->where('id_station_end', $stationEnd)
                    ->where('inter_station', false)
                    ->where('is_aller_retour', $isAllerRetour)
                    ->where('id', '!=', $trip->id)
                    ->first();

                if ($existingTrip) {
                    return response()->json([
                        'message' => 'Trip_already_exists',
                        'error' => 'Un trajet avec ces paramètres existe déjà.',
                        'existing_trip' => new TripResource($existingTrip)
                    ], 409);
                }

                $existingReverseTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationEnd)
                    ->where('id_station_end', $stationStart)
                    ->where('inter_station', false)
                    ->where('is_aller_retour', $isAllerRetour)
                    ->where('id', '!=', $trip->id)
                    ->first();

                if ($existingReverseTrip) {
                    return response()->json([
                        'message' => 'Reverse_trip_already_exists',
                        'error' => 'Un trajet inverse existe déjà pour cette ligne entre ces stations.',
                        'existing_trip' => new TripResource($existingReverseTrip),
                        'suggestion' => 'Vous pouvez utiliser le trajet existant en activant l\'option "trajet inversé".'
                    ], 409);
                }
            }

            $trip = $this->repository->update($data, $trip->id);

            return response()->json([
                'message' => 'Trip updated successfully',
                'data' => new TripResource($trip)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Trip $trip): JsonResponse
    {
        try {
            $this->repository->delete($trip->id);

            return response()->json([
                'message' => 'Trip deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_delete_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function checkTripExists(Request $request): JsonResponse
    {
        $request->validate([
            'id_line' => 'required|exists:lines,id',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id|different:id_station_start'
        ]);

        $lineId = $request->input('id_line');
        $stationStart = $request->input('id_station_start');
        $stationEnd = $request->input('id_station_end');

        $directTrip = Trip::where('id_line', $lineId)
            ->where('id_station_start', $stationStart)
            ->where('id_station_end', $stationEnd)
            ->where('inter_station', false)
            ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $reverseTrip = Trip::where('id_line', $lineId)
            ->where('id_station_start', $stationEnd)
            ->where('id_station_end', $stationStart)
            ->where('inter_station', false)
            ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $result = [
            'direct_trip_exists' => !is_null($directTrip),
            'reverse_trip_exists' => !is_null($reverseTrip),
            'direct_trip' => $directTrip ? new TripResource($directTrip) : null,
            'reverse_trip' => $reverseTrip ? new TripResource($reverseTrip) : null
        ];

        if ($directTrip || $reverseTrip) {
            $result['message'] = 'Des trajets existent déjà entre ces stations';
            if ($reverseTrip && !$directTrip) {
                $result['suggestion'] = 'Vous pouvez utiliser le trajet inverse existant en activant l\'option "trajet inversé" lors de la création de l\'abonnement.';
            }
        } else {
            $result['message'] = 'Aucun trajet n\'existe entre ces stations sur cette ligne';
            $result['can_create'] = true;
        }

        return response()->json($result);
    }
}




