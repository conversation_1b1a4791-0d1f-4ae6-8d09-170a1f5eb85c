<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AbnTypeSeeder extends Seeder
{
    public function run(): void
    {
        $abnTypes = [
            [
                'nom_fr' => 'Abonnement scolaire',
                'nom_en' => 'School Subscription',
                'nom_ar' => 'اشتراك مدرسي',
                'color' => '#ed8f7a',
                'is_student' => true,
                'hasCIN' => false,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement universitaire',
                'nom_en' => 'University Subscription',
                'nom_ar' => 'اشتراك جامعي',
                'color' => '#86bf91',
                'is_student' => true,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement civil',
                'nom_en' => 'Civil Subscription',
                'nom_ar' => 'اشتراك مدني',
                'color' => '#5563a4',
                'is_student' => false,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement impersonnel',
                'nom_en' => 'Impersonal Subscription',
                'nom_ar' => 'اشتراك غير شخصي',
                'color' => '#9c5f70',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => true,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement conventionné',
                'nom_en' => 'Contracted subscription',
                'nom_ar' => 'اشتراك تعاقدي',
                'color' => '#898458',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => false,
                'is_conventional' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('subs_types')->insert($abnTypes);
    }
}

