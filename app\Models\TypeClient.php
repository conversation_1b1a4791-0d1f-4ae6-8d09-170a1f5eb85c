<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class TypeClient extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'color',
        'is_student',
        'hasCIN',
        'is_impersonal',
        'is_conventional'
    ];

    protected $casts = [
        'is_student' => 'boolean',
        'hasCIN' => 'boolean',
        'is_impersonal' => 'boolean',
        'is_conventional' => 'boolean'
    ];

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class, 'id_client_type');
    }
}

