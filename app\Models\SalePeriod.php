<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class SalePeriod extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $table = 'sale_periods';

    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'date_start_client',
        'date_end_client',
        'date_start_agent',
        'date_end_agent',
        'date_start_validity',
        'date_end_validity',
        'id_periodicity',
        'id_campaign',
    ];

    protected $appends = ['status'];

    protected $casts = [
        'date_start_client' => 'date',
        'date_end_client' => 'date',
        'date_start_agent' => 'date',
        'date_end_agent' => 'date',
        'date_start_validity' => 'date',
        'date_end_validity' => 'date',
    ];

    /**
     * Get the periodicity that owns this sale period
     */
    public function periodicity(): BelongsTo
    {
        return $this->belongsTo(Periodicity::class, 'id_periodicity');
    }

    /**
     * Get the campaign that owns this sale period
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'id_campaign');
    }



    public function affectationAgents(): HasMany
    {
        return $this->hasMany(AffectationAgent::class, 'id_sale_period');
    }

    public function getStatusAttribute()
    {
        return now()->between($this->date_start, $this->date_end);
    }
}




