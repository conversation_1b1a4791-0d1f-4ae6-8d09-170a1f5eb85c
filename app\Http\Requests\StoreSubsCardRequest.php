<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubsCardRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_subscription' => 'required|exists:subscriptions,id',
            'id_motif_duplicate' => 'sometimes|required|exists:motif_duplicates,id',
            'type_amount' => 'sometimes|required|in:keep_old_amount,free_edit',
            'duplicate_amount' => 'sometimes|required|numeric'
        ];
    }
}