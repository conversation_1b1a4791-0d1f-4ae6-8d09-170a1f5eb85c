<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImportSocialAffairRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'file' => 'required|file|mimes:csv,txt,xlsx,xls',
            'governorate_id' => 'required|exists:governorates,id',
            'academic_year_id' => 'nullable|exists:academic_years,id'
        ];
    }
}
