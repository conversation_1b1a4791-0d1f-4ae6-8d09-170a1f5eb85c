<?php

namespace App\Policies;

use App\Models\Admin;

class AdminPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view_admins');
    }

    public function view(Admin $admin, Admin $model): bool
    {
        return $admin->hasPermissionTo('view_admins');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_admins');
    }

    public function update(Admin $admin, Admin $model): bool
    {
        return $admin->hasPermissionTo('edit_admins');
    }

    public function delete(Admin $admin, Admin $model): bool
    {
        return $admin->hasPermissionTo('delete_admins') && $admin->id !== $model->id;
    }

    public function assignRole(Admin $admin): bool
    {
        return $admin->hasPermissionTo('assign_roles');
    }
}
