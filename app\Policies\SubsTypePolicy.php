<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SubsType;

class SubsTypePolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_abn_types');
    }

    public function view(Admin $admin, SubsType $subsType): bool
    {
        return $admin->can('view_abn_types');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_abn_types');
    }

    public function update(Admin $admin, SubsType $subsType): bool
    {
        return $admin->can('edit_abn_types');
    }

    public function delete(Admin $admin, SubsType $subsType): bool
    {
        return $admin->can('delete_abn_types');
    }
}