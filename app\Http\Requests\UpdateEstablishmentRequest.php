<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEstablishmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'abbreviation' => 'required|string|max:255',
            'cnss_etab' => 'required|string|max:255',
            'id_delegation' => 'required|exists:delegations,id',
            'id_type_establishment' => 'required|exists:type_establishments,id'
        ];
    }
}
