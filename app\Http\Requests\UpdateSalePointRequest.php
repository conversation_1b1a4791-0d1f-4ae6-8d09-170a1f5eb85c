<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSalePointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'contact' => 'sometimes|string|max:255',
            'address' => 'sometimes|string|max:255',
            'status' => 'sometimes|boolean',
            'id_delegation' => 'sometimes|exists:delegations,id',
            'id_governorate' => 'sometimes|exists:governorates,id',
            'id_agency' => 'sometimes|exists:agencies,id'
        ];
    }
}