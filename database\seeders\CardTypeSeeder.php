<?php

namespace Database\Seeders;

use App\Models\CardType;
use Illuminate\Database\Seeder;

class CardTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define card types data
        $cardTypes = [
            // Normal card 
            [
                "id" => 1,
                'nom_fr' => 'Carte Normale',
                'nom_en' => 'Normal Card',
                'nom_ar' => 'بطاقة عادية',
                'code' => 'N'
            ],
            // Duplicate card
            [
                "id" => 2,
                'nom_fr' => 'Carte Dupliquée',
                'nom_en' => 'Duplicate Card',
                'nom_ar' => 'بطاقة مكررة',
                'code' => 'D'
            ],
        ];
        
        // Create card types
        foreach ($cardTypes as $cardType) {
            CardType::create($cardType);
        }
    }
}
