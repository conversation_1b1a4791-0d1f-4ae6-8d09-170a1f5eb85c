<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Audit extends Model
{
    protected $fillable = [
        'user_type',
        'user_id',
        'event',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'url',
        'ip_address',
        'user_agent',
        'tags'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user that performed the audit.
     */
    public function user(): MorphTo
    {
        return $this->morphTo('user');
    }

    /**
     * Get the auditable model.
     */
    public function auditable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the admin user if the user is an admin.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'user_id')->where('user_type', Admin::class);
    }

    /**
     * Get the client user if the user is a client.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'user_id')->where('user_type', Client::class);
    }

    /**
     * Scope to filter by event type.
     */
    public function scopeByEvent($query, $event)
    {
        return $query->where('event', $event);
    }

    /**
     * Scope to filter by auditable type.
     */
    public function scopeByAuditableType($query, $type)
    {
        return $query->where('auditable_type', $type);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId, $userType = null)
    {
        $query = $query->where('user_id', $userId);

        if ($userType) {
            $query = $query->where('user_type', $userType);
        }

        return $query;
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get the model name in a readable format.
     */
    public function getModelNameAttribute()
    {
        $modelClass = $this->auditable_type;

        if (!$modelClass) {
            return 'Unknown';
        }

        // Extract the class name from the full namespace
        $className = class_basename($modelClass);

        // Convert CamelCase to readable format
        return preg_replace('/(?<!^)[A-Z]/', ' $0', $className);
    }

    /**
     * Get the user name in a readable format.
     */
    public function getUserNameAttribute()
    {
        if (!$this->user) {
            return 'System';
        }

        if ($this->user instanceof Admin) {
            return $this->user->firstname . ' ' . $this->user->lastname;
        }

        if ($this->user instanceof Client) {
            if ($this->user->is_moral) {
                return $this->user->society_name;
            }
            return $this->user->firstname . ' ' . $this->user->lastname;
        }

        return 'Unknown User';
    }

    /**
     * Get the event in a readable format.
     */
    public function getEventNameAttribute()
    {
        $events = [
            'created' => 'Created',
            'updated' => 'Updated',
            'deleted' => 'Deleted',
            'restored' => 'Restored'
        ];

        return $events[$this->event] ?? ucfirst($this->event);
    }

    /**
     * Get the changes in a readable format.
     */
    public function getChangesAttribute()
    {
        $changes = [];

        if ($this->event === 'created') {
            return $this->new_values;
        }

        if ($this->event === 'updated' && $this->old_values && $this->new_values) {
            foreach ($this->new_values as $key => $newValue) {
                $oldValue = $this->old_values[$key] ?? null;

                if ($oldValue !== $newValue) {
                    $changes[$key] = [
                        'old' => $oldValue,
                        'new' => $newValue
                    ];
                }
            }
        }

        if ($this->event === 'deleted') {
            return $this->old_values;
        }

        return $changes;
    }
}
