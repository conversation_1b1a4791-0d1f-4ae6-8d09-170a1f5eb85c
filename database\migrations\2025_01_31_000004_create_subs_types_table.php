<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subs_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('color');
            $table->boolean('is_student');
            $table->boolean('hasCIN');
            $table->boolean('is_impersonal');
            $table->boolean('is_conventional');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subs_types');
    }
};
