<?php

namespace App\Http\Controllers;

use App\Models\AffectationAgent;
use App\Repositories\AffectationAgentRepository;
use App\Http\Requests\StoreAffectationAgentRequest;
use App\Http\Requests\UpdateAffectationAgentRequest;
use App\Http\Resources\AffectationAgentResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class AffectationAgentController extends Controller
{
    private AffectationAgentRepository $repository;

    public function __construct(AffectationAgentRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(AffectationAgent::class, 'affectation_agent');
    }

    public function all(): AnonymousResourceCollection
    {
        return AffectationAgentResource::collection(
            $this->repository->with(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])->all()
        );
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return AffectationAgentResource::collection(
            $this->repository->with(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])
                ->paginate($request->input('perPage', 15))
        );
    }

    public function store(StoreAffectationAgentRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationAgent = $this->repository->create([
                'id_agent' => $request->id_agent,
                'id_sale_point' => $request->id_sale_point,
                'id_sale_period' => $request->id_sale_period,
                'date_start' => $request->date_start,
                'date_end' => $request->date_end,
            ]);

            $affectationAgent->paymentMethods()->sync($request->payment_methods);

            DB::commit();

            return response()->json([
                'message' => 'Affectation created successfully',
                'data' => new AffectationAgentResource(
                    $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])
                )
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(AffectationAgent $affectationAgent): AffectationAgentResource
    {
        return new AffectationAgentResource(
            $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])
        );
    }

    public function update(UpdateAffectationAgentRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationAgent = $this->repository->find($id);
            
            if (!$affectationAgent) {
                return response()->json([
                    'message' => 'Affectation not found'
                ], 404);
            }

            $affectationAgent->update([
                'id_agent' => $request->id_agent,
                'id_sale_point' => $request->id_sale_point,
                'id_sale_period' => $request->id_sale_period,
                'date_start' => $request->date_start,
                'date_end' => $request->date_end,
            ]);

            $affectationAgent->paymentMethods()->sync($request->payment_methods);

            DB::commit();

            return response()->json([
                'message' => 'Affectation updated successfully',
                'data' => new AffectationAgentResource(
                    $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])
                )
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationAgent = $this->repository->find($id);
            
            if (!$affectationAgent) {
                return response()->json([
                    'message' => 'Affectation not found'
                ], 404);
            }
            $affectationAgent->delete();

            DB::commit();

            return response()->json([
                'message' => 'Affectation deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error deleting affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getAgentsBySalePoint($salePointId, $salePeriodId): JsonResponse
    {
        try {
            $affectations = $this->repository
                ->with(['agent', 'salePoint', 'salePeriod', 'paymentMethods'])
                ->findWhere([
                    'id_sale_point' => $salePointId,
                    'id_sale_period' => $salePeriodId
                ]);

            return response()->json([
                'success' => true,
                'message' => $affectations->isEmpty()
                    ? 'No agents found for this sale point and period'
                    : 'Agents retrieved successfully',
                'data' => AffectationAgentResource::collection($affectations)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving agents',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}



