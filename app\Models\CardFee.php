<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class CardFee extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'amount',
        'id_subs_type',
        'date_start',
        'date_end'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'date_start' => 'date',
        'date_end' => 'date'
    ];

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }
}
