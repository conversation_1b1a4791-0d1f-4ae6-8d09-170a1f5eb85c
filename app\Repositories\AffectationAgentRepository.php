<?php

namespace App\Repositories;

use App\Models\AffectationAgent;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class AffectationAgentRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_agent' => '=',
        'id_sale_point' => '=',
        'id_sale_period' => '=',
        'date_start' => 'between',
        'date_end' => 'between'
    ];

    public function model(): string
    {
        return AffectationAgent::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}


