<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubsCardRequest;
use App\Http\Resources\SubsCardResource;
use App\Models\AffectationAgent;
use App\Models\AffectationCardType;
use App\Models\CardFee;
use App\Models\CardType;
use App\Models\MotifDuplicate;
use App\Models\SalePeriod;
use App\Models\SubsCard;
use App\Models\Subscription;
use App\Repositories\SubsCardRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SubsCardController extends Controller
{
    private SubsCardRepository $repository;

    public function __construct(SubsCardRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SubsCard::class, 'subs_card');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return SubsCardResource::collection(
            $this->repository
                ->with(['cardType', 'subscription', 'motifDuplicate'])
                ->latest()
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return SubsCardResource::collection(
            $this->repository
                ->with(['cardType', 'subscription', 'motifDuplicate'])
                ->all()
        );
    }

    public function getSubsCardsBySubscription(int $subsId): AnonymousResourceCollection
    {
        return SubsCardResource::collection(
            $this->repository
                ->with(['cardType', 'motifDuplicate'])
                ->findWhere(['id_subscription' => $subsId])
        );
    }

    public function store(StoreSubsCardRequest $request): JsonResponse
    {
        $data = $request->validated();

        $card_type = CardType::find(1);

        $subscription = Subscription::find($data['id_subscription']);

        $card_fee = CardFee::where('id_subs_type', $subscription->id_subs_type)->first();

        $duplicate_amount = $card_fee->amount;

        if(isset($data['type_amount']) && isset($data['id_motif_duplicate'])){
            $motif = MotifDuplicate::find($data['id_motif_duplicate']);
            $card_type = $motif->cardType;

            if(isset($data['duplicate_amount']) && $data['type_amount'] == 'free_edit'){
                if($data['duplicate_amount']){
                    $duplicate_amount = $data['duplicate_amount'];
                } else {
                    $duplicate_amount = 0;
                }
            }
        }


        $data['id_card_type'] = $card_type->id;

        $data['duplicate_amount'] = $duplicate_amount;

        $admin =  $request->user();

        $data['id_agent'] = $admin->id;

        $affectation = AffectationAgent::where('id_agent', $admin->id)
        ->where('date_start', '<=', now())->where('date_end', '>=', now())
        ->whereHas('salePeriod', function ($query) {
            $query->where('date_start', '<=', now())->where('date_end', '>=', now());
        })
        ->first();

        if (!$affectation) {
            return response()->json([
                'message' => 'Affectation_not_found',
            ], 422);
        }

        $data['id_sale_point'] = $affectation->id_sale_point;

        $affectation_card_type = AffectationCardType::where('id_agent', $admin->id)
        ->where('date_start', '<=', now())->where('date_end', '>=', now())
        ->where('id_card_type', $card_type->id)
        ->first();

        if(!$affectation_card_type){
            return response()->json([
                'message' => 'Affectation_card_type_not_found',
            ], 422);
        }


        $data['id_affectation_card_type'] = $affectation_card_type->id;

        $current_serial_number = $affectation_card_type->current_serial_number;

        if ($current_serial_number > $affectation_card_type->end_serial_number) {
            return response()->json([
                'message' => 'no_more_cards',
            ], 422);
        }

        $data['ref'] = $current_serial_number;

        $current_serial_number_next = $current_serial_number + 1;

        $subsCard = $this->repository->create($data);

        if($subsCard){
            $affectation_card_type->update(['current_serial_number' => $current_serial_number_next]);

            if(!isset($data['id_motif_duplicate'])) {
                $subscription->update(['is_printed' => true]);
            }
        } else {
            return response()->json([
                'message' => 'card_creation_failed',
            ], 500);
        }

        return response()->json([
            'message' => 'Subscription card created successfully',
            'data' => new SubsCardResource($subsCard->load(['cardType', 'subscription', 'motifDuplicate']))
        ], 201);
    }

    public function show(SubsCard $subsCard): SubsCardResource
    {
        return new SubsCardResource(
            $subsCard->load(['cardType', 'subscription', 'motifDuplicate'])
        );
    }

    public function update(Request $request, SubsCard $subsCard): JsonResponse
    {
        $subsCard = $this->repository->update($request->validated(), $subsCard->id);

        return response()->json([
            'message' => 'Subscription card updated successfully',
            'data' => new SubsCardResource($subsCard->load(['cardType', 'subscription', 'motifDuplicate']))
        ]);
    }

    public function destroy(SubsCard $subsCard): JsonResponse
    {
        $this->repository->delete($subsCard->id);

        return response()->json([
            'message' => 'Subscription card deleted successfully'
        ]);
    }
}