<?php

namespace App\Repositories;


use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class StatisticsRepository
{
    /**
     * Get subscriptions by type
     */
    public function getSubscriptionsByType(Carbon $startDate, Carbon $endDate): array
    {
        return Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
            ->whereBetween('subscriptions.created_at', [$startDate, $endDate])
            ->select('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar', DB::raw('count(*) as count'))
            ->groupBy('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar')
            ->get()
            ->toArray();
    }

    /**
     * Get date format for SQL query based on group by parameter
     */
    private function getDateFormat(string $groupBy): string
    {
        switch ($groupBy) {
            case 'day':
                return '%Y-%m-%d';
            case 'week':
                return '%Y-%u';
            case 'year':
                return '%Y';
            case 'month':
            default:
                return '%Y-%m';
        }
    }
}
