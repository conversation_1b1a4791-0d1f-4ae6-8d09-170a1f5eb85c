<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAdminRequest;
use App\Http\Requests\UpdateAdminRequest;
use App\Http\Resources\AdminResource;
use App\Models\Admin;
use App\Repositories\AdminRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(AdminRepository::class);
        $this->authorizeResource(Admin::class, 'admin');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $admins = $this->repository
        ->orderBy('created_at', 'desc')
            ->with(['roles', 'permissions'])
            ->latest()
            ->paginate($request->input('perPage'));

        return AdminResource::collection($admins);
    }

    public function all(): AnonymousResourceCollection
    {
        return AdminResource::collection(
            $this->repository->all()
        );
    }

    public function store(StoreAdminRequest $request): JsonResponse
    {
        $data = $request->validated();
        unset($data['confirmPassword']);
        $data['password'] = Hash::make($data['password']);

        $admin = $this->repository->create($data);

        if (isset($data['roles'])) {
            $this->authorize('assignRole', Admin::class);
            $admin->syncRoles($data['roles']);
        }

        $admin->load(['roles', 'permissions']);

        return response()->json([
            'message' => 'Admin created successfully',
            'data' => new AdminResource($admin)
        ], 201);
    }

    public function show(Admin $admin): AdminResource
    {
        $admin->load(['roles', 'permissions']);
        return new AdminResource($admin);
    }

    public function update(UpdateAdminRequest $request, Admin $admin): JsonResponse
    {
        $data = $request->validated();

        if (isset($data['confirmPassword'])) {
            unset($data['confirmPassword']);
        }
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $admin = $this->repository->update($data, $admin->id);

        if (isset($data['roles'])) {
            $this->authorize('assignRole', Admin::class);
            $admin->syncRoles($data['roles']);
        }

        $admin->load(['roles', 'permissions']);

        return response()->json([
            'message' => 'Admin updated successfully',
            'data' => new AdminResource($admin)
        ]);
    }

    public function destroy(Admin $admin): JsonResponse
    {
        if ($admin->id === auth()->id()) {
            return response()->json([
                'message' => 'Cannot delete your own account'
            ], 403);
        }

        $this->repository->delete($admin->id);

        return response()->json([
            'message' => 'Admin deleted successfully'
        ]);
    }

    public function assignRoles(Request $request, Admin $admin): JsonResponse
    {
        $this->authorize('assignRole', Admin::class);

        $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);

        $admin->syncRoles($request->roles);
        $admin->load(['roles', 'permissions']);

        return response()->json([
            'message' => 'Roles assigned successfully',
            'data' => new AdminResource($admin)
        ]);
    }
}


